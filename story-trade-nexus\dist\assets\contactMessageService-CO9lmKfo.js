const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js"])))=>i.map(i=>d[i]);
import{w as d,_ as g,x as m}from"./index-beYRG-mR.js";const y=async e=>{try{await d();const{collection:s,addDoc:t,serverTimestamp:o}=await g(async()=>{const{collection:n,addDoc:l,serverTimestamp:a}=await import("./index.esm-ehpEbksy.js");return{collection:n,addDoc:l,serverTimestamp:a}},__vite__mapDeps([0,1]));console.log("Submitting contact message:",e);const r=s(m,"contactMessages"),c={...e,createdAt:o(),isRead:!1},i=await t(r,c);return console.log("Contact message submitted successfully with ID:",i.id),i.id}catch(s){throw console.error("Error submitting contact message:",s),s}},h=async()=>{try{await d();const{collection:e,query:s,getDocs:t,orderBy:o}=await g(async()=>{const{collection:l,query:a,getDocs:u,orderBy:w}=await import("./index.esm-ehpEbksy.js");return{collection:l,query:a,getDocs:u,orderBy:w}},__vite__mapDeps([0,1]));console.log("Fetching all contact messages from Firestore");const r=e(m,"contactMessages"),c=s(r,o("createdAt","desc")),i=await t(c),n=[];return i.forEach(l=>{const a=l.data();n.push({id:l.id,email:a.email||"",phone:a.phone||"",message:a.message||"",createdAt:a.createdAt,isRead:a.isRead||!1,readAt:a.readAt||null})}),console.log(`Fetched ${n.length} contact messages`),n}catch(e){throw console.error("Error fetching contact messages:",e),e}},p=async e=>{try{await d();const{doc:s,updateDoc:t,serverTimestamp:o}=await g(async()=>{const{doc:c,updateDoc:i,serverTimestamp:n}=await import("./index.esm-ehpEbksy.js");return{doc:c,updateDoc:i,serverTimestamp:n}},__vite__mapDeps([0,1]));console.log(`Marking message ${e} as read`);const r=s(m,"contactMessages",e);await t(r,{isRead:!0,readAt:o()}),console.log(`Message ${e} marked as read successfully`)}catch(s){throw console.error(`Error marking message ${e} as read:`,s),s}},f=async e=>{try{await d();const{doc:s,updateDoc:t}=await g(async()=>{const{doc:r,updateDoc:c}=await import("./index.esm-ehpEbksy.js");return{doc:r,updateDoc:c}},__vite__mapDeps([0,1]));console.log(`Marking message ${e} as unread`);const o=s(m,"contactMessages",e);await t(o,{isRead:!1,readAt:null}),console.log(`Message ${e} marked as unread successfully`)}catch(s){throw console.error(`Error marking message ${e} as unread:`,s),s}};export{f as a,h as g,p as m,y as s};
