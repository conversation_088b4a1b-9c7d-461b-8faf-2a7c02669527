{"version": 3, "sources": ["../../@radix-ui/react-roving-focus/src/RovingFocusGroup.tsx", "../../@radix-ui/react-roving-focus/node_modules/@radix-ui/react-context/src/createContext.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId = null, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId,\n    onChange: onCurrentTabStopIdChange,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends PrimitiveSpanProps {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAaZ,yBAAA;AA0BX,SAAS,mBAAmB,WAAmB,yBAAwC,CAAC,GAAG;AACzF,MAAI,kBAAyB,CAAC;AAM9B,WAASC,eACP,mBACA,gBACA;AACA,UAAM,cAAoB,oBAA4C,cAAc;AACpF,UAAM,QAAQ,gBAAgB;AAC9B,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AAErD,aAAS,SACP,OACA;AACA,YAAM,EAAE,OAAO,UAAU,GAAG,QAAQ,IAAI;AACxC,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAG7C,YAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,iBAAO,wBAAC,QAAQ,UAAR,EAAiB,OAAe,SAAA,CAAS;IACnD;AAEA,aAASC,YAAW,cAAsB,OAA4C;AACpF,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAC7C,YAAM,UAAgB,iBAAW,OAAO;AACxC,UAAI,QAAS,QAAO;AACpB,UAAI,mBAAmB,OAAW,QAAO;AAEzC,YAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;IACpF;AAEA,aAAS,cAAc,oBAAoB;AAC3C,WAAO,CAAC,UAAUA,WAAU;EAC9B;AAMA,QAAM,cAA2B,MAAM;AACrC,UAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,aAAa,oBAAc,cAAc;IAC3C,CAAC;AACD,WAAO,SAAS,SAAS,OAAc;AACrC,YAAM,YAAW,+BAAQ,eAAc;AACvC,aAAa;QACX,OAAO,EAAE,CAAC,UAAU,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS,EAAE;QACtE,CAAC,OAAO,QAAQ;MAClB;IACF;EACF;AAEA,cAAY,YAAY;AACxB,SAAO,CAACD,gBAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;AACrF;AAMA,SAAS,wBAAwB,QAAuB;AACtD,QAAM,YAAY,OAAO,CAAC;AAC1B,MAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,QAAM,cAA2B,MAAM;AACrC,UAAM,aAAa,OAAO,IAAI,CAACE,kBAAiB;MAC9C,UAAUA,aAAY;MACtB,WAAWA,aAAY;IACzB,EAAE;AAEF,WAAO,SAAS,kBAAkB,gBAAgB;AAChD,YAAM,aAAa,WAAW,OAAO,CAACC,aAAY,EAAE,UAAU,UAAU,MAAM;AAI5E,cAAM,aAAa,SAAS,cAAc;AAC1C,cAAM,eAAe,WAAW,UAAU,SAAS,EAAE;AACrD,eAAO,EAAE,GAAGA,aAAY,GAAG,aAAa;MAC1C,GAAG,CAAC,CAAC;AAEL,aAAa,cAAQ,OAAO,EAAE,CAAC,UAAU,UAAU,SAAS,EAAE,GAAG,WAAW,IAAI,CAAC,UAAU,CAAC;IAC9F;EACF;AAEA,cAAY,YAAY,UAAU;AAClC,SAAO;AACT;;;ADxDU,IAAAC,sBAAA;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB,EAAE,SAAS,OAAO,YAAY,KAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,IAAI;EACnE;EACA,CAAC,qBAAqB;AACxB;AA8BA,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,8BAAkD,UAAU;AAK9D,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,eACE,yBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,yBAChC,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,yBAC5B,cAAA,yBAAC,sBAAA,EAAsB,GAAG,OAAO,KAAK,aAAA,CAAc,EAAA,CACtD,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAc;AAgB/B,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;IACJ;IACA;IACA,OAAO;IACP;IACA,kBAAkB;IAClB;IACA;IACA;IACA,4BAA4B;IAC5B,GAAG;EACL,IAAI;AACJ,QAAM,MAAY,cAAoC,IAAI;AAC1D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,YAAY,aAAa,GAAG;AAClC,QAAM,CAAC,mBAAmB,MAAM,mBAAmB,IAAI,qBAAqB;IAC1E,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,KAAK;AACpE,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,WAAW,cAAc,uBAAuB;AACtD,QAAM,kBAAwB,cAAO,KAAK;AAC1C,QAAM,CAAC,qBAAqB,sBAAsB,IAAU,gBAAS,CAAC;AAEhE,EAAA,iBAAU,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,WAAK,iBAAiB,aAAa,gBAAgB;AACnD,aAAO,MAAM,KAAK,oBAAoB,aAAa,gBAAgB;IACrE;EACF,GAAG,CAAC,gBAAgB,CAAC;AAErB,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA,KAAK;MACL;MACA;MACA,aAAmB;QACjB,CAAC,cAAc,oBAAoB,SAAS;QAC5C,CAAC,mBAAmB;MACtB;MACA,gBAAsB,mBAAY,MAAM,oBAAoB,IAAI,GAAG,CAAC,CAAC;MACrE,oBAA0B;QACxB,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;QACzD,CAAC;MACH;MACA,uBAA6B;QAC3B,MAAM,uBAAuB,CAAC,cAAc,YAAY,CAAC;QACzD,CAAC;MACH;MAEA,cAAA;QAAC,UAAU;QAAV;UACC,UAAU,oBAAoB,wBAAwB,IAAI,KAAK;UAC/D,oBAAkB;UACjB,GAAG;UACJ,KAAK;UACL,OAAO,EAAE,SAAS,QAAQ,GAAG,MAAM,MAAM;UACzC,aAAa,qBAAqB,MAAM,aAAa,MAAM;AACzD,4BAAgB,UAAU;UAC5B,CAAC;UACD,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AAKtD,kBAAM,kBAAkB,CAAC,gBAAgB;AAEzC,gBAAI,MAAM,WAAW,MAAM,iBAAiB,mBAAmB,CAAC,kBAAkB;AAChF,oBAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;AAClE,oBAAM,cAAc,cAAc,eAAe;AAEjD,kBAAI,CAAC,gBAAgB,kBAAkB;AACrC,sBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,sBAAM,aAAa,MAAM,KAAK,CAAC,SAAS,KAAK,MAAM;AACnD,sBAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,OAAO,gBAAgB;AACrE,sBAAM,iBAAiB,CAAC,YAAY,aAAa,GAAG,KAAK,EAAE;kBACzD;gBACF;AACA,sBAAM,iBAAiB,eAAe,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AACrE,2BAAW,gBAAgB,yBAAyB;cACtD;YACF;AAEA,4BAAgB,UAAU;UAC5B,CAAC;UACD,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,oBAAoB,KAAK,CAAC;QAAA;MAC7E;IAAA;EACF;AAEJ,CAAC;AAMD,IAAM,YAAY;AAUlB,IAAM,uBAA6B;EACjC,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;MACJ;MACA,YAAY;MACZ,SAAS;MACT;MACA,GAAG;IACL,IAAI;AACJ,UAAM,SAAS,MAAM;AACrB,UAAM,KAAK,aAAa;AACxB,UAAM,UAAU,sBAAsB,WAAW,uBAAuB;AACxE,UAAM,mBAAmB,QAAQ,qBAAqB;AACtD,UAAM,WAAW,cAAc,uBAAuB;AAEtD,UAAM,EAAE,oBAAoB,sBAAsB,IAAI;AAEhD,IAAA,iBAAU,MAAM;AACpB,UAAI,WAAW;AACb,2BAAmB;AACnB,eAAO,MAAM,sBAAsB;MACrC;IACF,GAAG,CAAC,WAAW,oBAAoB,qBAAqB,CAAC;AAEzD,eACE;MAAC,WAAW;MAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,cAAA;UAAC,UAAU;UAAV;YACC,UAAU,mBAAmB,IAAI;YACjC,oBAAkB,QAAQ;YACzB,GAAG;YACJ,KAAK;YACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAG9D,kBAAI,CAAC,UAAW,OAAM,eAAe;kBAEhC,SAAQ,YAAY,EAAE;YAC7B,CAAC;YACD,SAAS,qBAAqB,MAAM,SAAS,MAAM,QAAQ,YAAY,EAAE,CAAC;YAC1E,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,MAAM,QAAQ,SAAS,MAAM,UAAU;AACzC,wBAAQ,eAAe;AACvB;cACF;AAEA,kBAAI,MAAM,WAAW,MAAM,cAAe;AAE1C,oBAAM,cAAc,eAAe,OAAO,QAAQ,aAAa,QAAQ,GAAG;AAE1E,kBAAI,gBAAgB,QAAW;AAC7B,oBAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,MAAM,SAAU;AACtE,sBAAM,eAAe;AACrB,sBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,SAAS;AACxD,oBAAI,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAE1D,oBAAI,gBAAgB,OAAQ,gBAAe,QAAQ;yBAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;AACzD,sBAAI,gBAAgB,OAAQ,gBAAe,QAAQ;AACnD,wBAAM,eAAe,eAAe,QAAQ,MAAM,aAAa;AAC/D,mCAAiB,QAAQ,OACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,MAAM,eAAe,CAAC;gBAC3C;AAMA,2BAAW,MAAM,WAAW,cAAc,CAAC;cAC7C;YACF,CAAC;UAAA;QACH;MAAA;IACF;EAEJ;AACF;AAEA,qBAAqB,cAAc;AAKnC,IAAM,0BAAuD;EAC3D,WAAW;EAAQ,SAAS;EAC5B,YAAY;EAAQ,WAAW;EAC/B,QAAQ;EAAS,MAAM;EACvB,UAAU;EAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,KAAa,KAAiB;AAC1D,MAAI,QAAQ,MAAO,QAAO;AAC1B,SAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,OAA4B,aAA2B,KAAiB;AAC9F,QAAM,MAAM,qBAAqB,MAAM,KAAK,GAAG;AAC/C,MAAI,gBAAgB,cAAc,CAAC,aAAa,YAAY,EAAE,SAAS,GAAG,EAAG,QAAO;AACpF,MAAI,gBAAgB,gBAAgB,CAAC,WAAW,WAAW,EAAE,SAAS,GAAG,EAAG,QAAO;AACnF,SAAO,wBAAwB,GAAG;AACpC;AAEA,SAAS,WAAW,YAA2B,gBAAgB,OAAO;AACpE,QAAM,6BAA6B,SAAS;AAC5C,aAAW,aAAa,YAAY;AAElC,QAAI,cAAc,2BAA4B;AAC9C,cAAU,MAAM,EAAE,cAAc,CAAC;AACjC,QAAI,SAAS,kBAAkB,2BAA4B;EAC7D;AACF;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO;", "names": ["React", "createContext", "useContext", "createScope", "nextScopes", "import_jsx_runtime"]}