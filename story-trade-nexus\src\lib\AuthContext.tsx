import React, { createContext, useContext, useState, useEffect } from 'react';
import { auth, initializeFirebase } from './firebase';
import { createUserDocument, getUserDocument, UserData, isUserAdmin } from './userService';
import { UserRole } from '@/types';

// Define User type to avoid importing from firebase/auth
type User = {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
};

interface AdditionalUserData {
  phone?: string;
  address?: string;
  apartment?: string;
  city?: string;
  state?: string;
  pincode?: string;
  community?: string;
  gpsCoordinates?: any;
  [key: string]: any;
}

interface AuthContextType {
  currentUser: User | null;
  userData: UserData | null;
  loading: boolean;
  emailVerified: boolean;
  isAdmin: boolean;
  signUp: (
    email: string,
    password: string,
    displayName: string,
    additionalData?: AdditionalUserData
  ) => Promise<User>;
  signIn: (email: string, password: string) => Promise<User>;
  signOut: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  reloadUser: () => Promise<boolean>;
  checkAdminStatus: () => Promise<boolean>;
  refreshUserData: () => Promise<UserData | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Function to check if the current user is an admin
  const checkAdminStatus = async (): Promise<boolean> => {
    if (!currentUser) {
      console.log("checkAdminStatus: No current user");
      setIsAdmin(false);
      return false;
    }

    try {
      console.log("checkAdminStatus: Checking admin status for user:", currentUser.uid);
      console.log("checkAdminStatus: User email:", currentUser.email);

      // Special <NAME_EMAIL> - always make them admin
      if (currentUser.email === '<EMAIL>') {
        console.log("checkAdminStatus: Special admin user detected");

        try {
          // Dynamically import Firestore functions
          const { doc, setDoc, getDoc } = await import('firebase/firestore');

          // Get the user document reference
          const userRef = doc(db, 'users', currentUser.uid);

          // Check if the document exists
          const docSnap = await getDoc(userRef);

          if (docSnap.exists()) {
            const userData = docSnap.data();
            console.log("checkAdminStatus: Existing user document:", userData);

            // If user doesn't have admin role, update it
            if (userData.role !== UserRole.Admin) {
              console.log("checkAdminStatus: Updating special user to admin role");
              await setDoc(userRef, { role: UserRole.Admin }, { merge: true });
              console.log("checkAdminStatus: Special user updated to admin role");
            }
          } else {
            // Create a new user document with admin role
            console.log("checkAdminStatus: Creating new user document with admin role");
            await setDoc(userRef, {
              uid: currentUser.uid,
              email: currentUser.email,
              displayName: currentUser.displayName || 'Admin User',
              role: UserRole.Admin,
              createdAt: new Date().toISOString()
            });
            console.log("checkAdminStatus: New admin user document created");
          }

          // Refresh user data after update
          const updatedUserDoc = await getUserDocument(currentUser.uid);
          console.log("checkAdminStatus: Updated user document:", updatedUserDoc);
          setUserData(updatedUserDoc);

        } catch (updateError) {
          console.error("checkAdminStatus: Error updating special user role:", updateError);
          // Even if the update fails, still grant admin access to this special user
        }

        // Always set admin status to true for this special user
        setIsAdmin(true);
        return true;
      }

      // For other users, first try to get the user document directly
      const userDoc = await getUserDocument(currentUser.uid);
      console.log("checkAdminStatus: User document:", userDoc);

      // Check their role in the user document
      if (userDoc) {
        const adminStatus = userDoc.role === UserRole.Admin;
        console.log("checkAdminStatus: User admin status from document:", adminStatus);
        setIsAdmin(adminStatus);
        return adminStatus;
      }

      // If no user document found, fall back to isUserAdmin function
      console.log("checkAdminStatus: No user document found, falling back to isUserAdmin");
      const adminStatus = await isUserAdmin(currentUser.uid);
      console.log("checkAdminStatus: Admin status from isUserAdmin:", adminStatus);
      setIsAdmin(adminStatus);
      return adminStatus;
    } catch (error) {
      console.error("checkAdminStatus: Error checking admin status:", error);
      setIsAdmin(false);
      return false;
    }
  };

  useEffect(() => {
    let unsubscribe: () => void = () => {};

    const setupAuthObserver = async () => {
      try {
        // Initialize Firebase
        await initializeFirebase();

        // Dynamically import Firebase auth functions
        const { onAuthStateChanged } = await import('firebase/auth');

        console.log("Setting up auth state observer...");
        unsubscribe = onAuthStateChanged(auth, async (user) => {
          console.log("Auth state changed:", user ? `User: ${user.uid}` : "No user");
          setCurrentUser(user);

          if (user) {
            // Set email verification status immediately
            setEmailVerified(user.emailVerified);
            console.log("Email verification status:", user.emailVerified);

            // Special <NAME_EMAIL> - set admin immediately
            if (user.email === '<EMAIL>') {
              console.log("Special admin user detected - setting admin status immediately");
              setIsAdmin(true);
              setLoading(false); // Set loading to false immediately for admin user

              // Async operations for admin user (don't block UI)
              (async () => {
                try {
                  await checkAdminStatus(); // Ensure admin role is set in database
                  const userDoc = await getUserDocument(user.uid);
                  setUserData(userDoc);
                } catch (error) {
                  console.error("Error in admin user setup:", error);
                }
              })();
              return;
            }

            // For other users, fetch data asynchronously
            try {
              // Get user data from Firestore
              console.log("Fetching user data from Firestore...");
              const userDoc = await getUserDocument(user.uid);
              setUserData(userDoc);
              console.log("User data:", userDoc);

              // If user document doesn't exist, create it
              if (!userDoc) {
                try {
                  console.log("Creating user document for existing user");
                  await createUserDocument(user);
                  const newUserDoc = await getUserDocument(user.uid);
                  setUserData(newUserDoc);
                  console.log("New user document created:", newUserDoc);
                } catch (createError) {
                  console.error("Error creating user document for existing user:", createError);
                }
              }

              // Check if user is an admin from the document
              const adminStatus = userDoc?.role === UserRole.Admin;
              setIsAdmin(adminStatus);
              console.log("User admin status:", adminStatus);
            } catch (error) {
              console.error("Error fetching user data:", error);
              // Continue without throwing - user is still authenticated
            }
          } else {
            setUserData(null);
            setEmailVerified(false);
            setIsAdmin(false);
            console.log("User signed out or no user");
          }

          setLoading(false);
        });
      } catch (error) {
        console.error("Error setting up auth observer:", error);
        setLoading(false);
      }
    };

    setupAuthObserver();

    return () => unsubscribe();
  }, []);

  // Sign up with email and password
  const signUp = async (
    email: string,
    password: string,
    displayName: string,
    additionalData?: AdditionalUserData
  ) => {
    console.log("AuthContext signUp called with:", { email, displayName });
    console.log("Additional data:", additionalData);

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        createUserWithEmailAndPassword,
        updateProfile,
        sendEmailVerification
      } = await import('firebase/auth');

      // First create the user with Firebase Authentication
      console.log("Creating user with Firebase Authentication...");
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      console.log("User created in Firebase Auth:", userCredential.user.uid);

      try {
        // Update the user's profile with the display name
        console.log("Updating user profile with display name:", displayName);
        await updateProfile(userCredential.user, { displayName });
        console.log("User profile updated successfully");

        // Send email verification
        try {
          console.log("Sending verification email...");
          await sendEmailVerification(userCredential.user);
          console.log("Verification email sent successfully");
        } catch (verificationError) {
          console.error("Error sending verification email:", verificationError);
          // Continue without throwing - user is still created
        }

        // Create user document in Firestore
        // If Firestore operations fail, we still have the authenticated user
        try {
          console.log("Creating user document in Firestore...");
          await createUserDocument(userCredential.user, additionalData);
          console.log("User document created successfully in Firestore");
        } catch (firestoreError) {
          console.error("Error creating user document in Firestore:", firestoreError);
          // Continue without throwing - user is still authenticated
        }
      } catch (profileError) {
        console.error("Error updating user profile:", profileError);
        // Continue without throwing - user is still authenticated
      }

      console.log("User registration completed successfully");
      return userCredential.user;
    } catch (error) {
      console.error("Error signing up:", error);
      throw error;
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        signInWithEmailAndPassword,
        reload: firebaseReloadUser
      } = await import('firebase/auth');

      // Authenticate the user
      console.log("Signing in user with email:", email);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log("User signed in successfully:", userCredential.user.uid);

      // Reload the user to get the latest verification status
      try {
        await firebaseReloadUser(userCredential.user);
      } catch (reloadError) {
        console.error("Error reloading user after sign in:", reloadError);
        // Continue with the current user state
      }

      // Check if email is verified
      if (!userCredential.user.emailVerified) {
        setEmailVerified(false);
        console.log("User email is not verified");
      } else {
        setEmailVerified(true);
        console.log("User email is verified");
      }

      try {
        // Get user data from Firestore
        console.log("Fetching user data from Firestore...");
        const userDoc = await getUserDocument(userCredential.user.uid);
        setUserData(userDoc);
        console.log("User data fetched successfully:", userDoc);
      } catch (firestoreError) {
        console.error("Error fetching user data from Firestore:", firestoreError);
        // Continue without throwing - user is still authenticated
      }

      return userCredential.user;
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { signOut: firebaseSignOut } = await import('firebase/auth');

      await firebaseSignOut(auth);
      setUserData(null);
      setEmailVerified(false);
      console.log("User signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  };

  // Send verification email
  const sendVerificationEmail = async () => {
    if (!currentUser) {
      throw new Error("No user is currently signed in");
    }

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { sendEmailVerification } = await import('firebase/auth');

      await sendEmailVerification(currentUser);
      console.log("Verification email sent successfully");
    } catch (error) {
      console.error("Error sending verification email:", error);
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { sendPasswordResetEmail } = await import('firebase/auth');

      await sendPasswordResetEmail(auth, email);
      console.log("Password reset email sent successfully");
    } catch (error) {
      console.error("Error sending password reset email:", error);
      throw error;
    }
  };

  // Reload user to check for updated email verification status
  const reloadUser = async (): Promise<boolean> => {
    if (!currentUser) {
      console.error("No user is currently signed in");
      return false;
    }

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { reload: firebaseReloadUser } = await import('firebase/auth');

      // Reload the user object from Firebase
      await firebaseReloadUser(currentUser);
      console.log("User reloaded successfully");

      // Get the current user again from auth to ensure we have the latest data
      const user = auth.currentUser;

      if (!user) {
        console.error("User is null after reload");
        setEmailVerified(false);
        return false;
      }

      // Update the email verification status
      const isVerified = user.emailVerified;
      console.log(`User email verification status: ${isVerified}`);

      // Set the emailVerified state
      setEmailVerified(isVerified);

      return isVerified;
    } catch (error) {
      console.error("Error reloading user:", error);
      // In case of error, default to not verified for safety
      setEmailVerified(false);
      return false;
    }
  };

  // Refresh user data from Firestore
  const refreshUserData = async (): Promise<UserData | null> => {
    if (!currentUser) {
      console.error("No user is currently signed in");
      return null;
    }

    try {
      // Get user data from Firestore
      const userDoc = await getUserDocument(currentUser.uid);
      setUserData(userDoc);
      return userDoc;
    } catch (error) {
      console.error("Error refreshing user data:", error);
      return null;
    }
  };

  // Social login methods have been removed

  const value = {
    currentUser,
    userData,
    loading,
    emailVerified,
    isAdmin,
    signUp,
    signIn,
    signOut,
    sendVerificationEmail,
    resetPassword,
    reloadUser,
    checkAdminStatus,
    refreshUserData
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
