/**
 * BookSortSelector Component
 *
 * A dropdown component for selecting book sorting criteria.
 * Integrates with the existing PeerBooks UI design and color scheme.
 */

import React from 'react';
import { ArrowUpDown, Check } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  SortCriteria,
  SortOption,
  SORT_OPTIONS,
  getAvailableSortOptions
} from '@/lib/bookSortingUtils';
import { Book } from '@/types/index';

interface BookSortSelectorProps {
  /** Current sort criteria */
  sortCriteria: SortCriteria;
  /** Callback when sort criteria changes */
  onSortChange: (criteria: SortCriteria) => void;
  /** Array of books to determine available sort options */
  books: Book[];
  /** Whether the selector is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
}

const BookSortSelector: React.FC<BookSortSelectorProps> = ({
  sortCriteria,
  onSortChange,
  books,
  disabled = false,
  className = ''
}) => {
  // Get available sort options based on current book data
  const availableOptions = getAvailableSortOptions(books);

  // Find the current sort option
  const currentOption = SORT_OPTIONS.find(option => option.value === sortCriteria);

  // Count books that would be affected by different sort criteria
  const getBookCount = (criteria: SortCriteria): number => {
    switch (criteria) {
      case 'price-low-high':
      case 'price-high-low':
        return books.filter(book =>
          book.availability.includes('Sale') && book.price
        ).length;

      case 'rental-low-high':
      case 'rental-high-low':
        return books.filter(book =>
          book.availability.includes('Rent') && book.rentalPrice
        ).length;

      case 'distance':
      case 'community-distance':
        return books.filter(book => book.distance !== undefined).length;

      default:
        return books.length;
    }
  };

  return (
    <div className={className}>
      <Select
        value={sortCriteria}
        onValueChange={(value) => onSortChange(value as SortCriteria)}
        disabled={disabled}
      >
        <SelectTrigger className="h-10 w-full">
          <div className="flex items-center gap-2 w-full">
            <ArrowUpDown className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <SelectValue placeholder="Sort by...">
              <span className="text-sm">
                {currentOption?.label || 'Sort by...'}
              </span>
            </SelectValue>
          </div>
        </SelectTrigger>

        <SelectContent>
          {availableOptions.map((option) => {
            const bookCount = getBookCount(option.value);
            const isSelected = option.value === sortCriteria;

            return (
              <SelectItem
                key={option.value}
                value={option.value}
                className="cursor-pointer"
              >
                <div className="flex items-start justify-between w-full">
                  <div className="flex flex-col flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{option.label}</span>
                      {isSelected && (
                        <Check className="h-4 w-4 text-burgundy-600" />
                      )}
                    </div>
                    <span className="text-xs text-gray-500 mt-1">
                      {option.description}
                    </span>
                    {bookCount < books.length && (
                      <span className="text-xs text-blue-600 mt-1">
                        {bookCount} of {books.length} books have this data
                      </span>
                    )}
                  </div>
                </div>
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>
  );
};

export default BookSortSelector;
