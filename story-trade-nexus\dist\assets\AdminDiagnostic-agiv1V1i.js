import{u as $,r as n,U as Y,j as e,H as O,h as o,N as v,v as T,C as V,L as A,y as H,J as c,O as w,Q as z}from"./index-D8cEQRhY.js";import{S as D}from"./shield-2-O4dxeE.js";import{C as F}from"./circle-alert-DKRyYgcc.js";const G=()=>{const{currentUser:s,isAdmin:d,checkAdminStatus:h,refreshUserData:R}=$(),[u,f]=n.useState(!1),[j,N]=n.useState(!1),[r,b]=n.useState(null),[S,p]=n.useState(null),[y,l]=n.useState([]),[C,E]=n.useState(!1);n.useEffect(()=>{const i=console.log,a=console.error;return console.log=(...t)=>{i(...t),l(x=>[...x,`LOG: ${t.map(g=>String(g)).join(" ")}`])},console.error=(...t)=>{a(...t),l(x=>[...x,`ERROR: ${t.map(g=>String(g)).join(" ")}`])},()=>{console.log=i,console.error=a}},[]);const U=async()=>{if(!s){c.error("You must be signed in to run diagnostics");return}try{N(!0),l([]),console.log("Diagnostic: Current user:",s.uid),console.log("Diagnostic: User email:",s.email),console.log("Diagnostic: Email verified:",s.emailVerified),console.log("Diagnostic: Admin status from context:",d),console.log("Diagnostic: Fetching user document from Firestore...");const i=await w(s.uid);b(i),console.log("Diagnostic: User document:",i),console.log("Diagnostic: Checking admin status from server...");const a=await h();p(a),console.log("Diagnostic: Admin status from server:",a)}catch(i){console.error("Diagnostic: Error running diagnostics:",i)}finally{N(!1)}},k=async()=>{if(!s){c.error("You must be signed in to fix admin access");return}try{f(!0),l([]),console.log("Fix: Attempting to fix admin access for:",s.email),await z(s.uid),console.log("Fix: User set as admin in database"),await R(),console.log("Fix: User data refreshed");const i=await h();p(i),console.log("Fix: Admin status after fix:",i);const a=await w(s.uid);b(a),console.log("Fix: Updated user document:",a),E(!0),c.success("Admin access fix attempted")}catch(i){console.error("Fix: Error fixing admin access:",i),c.error("Failed to fix admin access")}finally{f(!1)}},m=(s==null?void 0:s.email)==="<EMAIL>",L=(r==null?void 0:r.role)===Y.Admin;return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(O,{}),e.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:e.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(D,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Access Diagnostic"}),e.jsxs("p",{className:"text-gray-600",children:["Troubleshooting admin access for ",(s==null?void 0:s.email)||"Not signed in"]})]}),s?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"User Information:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"User ID:"}),e.jsx("span",{className:"text-gray-700",children:s.uid})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email:"}),e.jsx("span",{className:"text-gray-700",children:s.email})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email Verified:"}),e.jsx("span",{className:`${s.emailVerified?"text-green-600":"text-red-600"}`,children:s.emailVerified?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Admin Status:"}),e.jsx("span",{className:`${d?"text-green-600":"text-red-600"}`,children:d?"Yes":"No"})]})]})]}),r&&e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Firestore Document:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Role:"}),e.jsx("span",{className:`${L?"text-green-600":"text-red-600"}`,children:r.role||"Not set"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Display Name:"}),e.jsx("span",{className:"text-gray-700",children:r.displayName||"Not set"})]})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-center gap-4 mb-6",children:[e.jsx(o,{onClick:U,disabled:j,className:"flex items-center",children:j?e.jsxs(e.Fragment,{children:[e.jsx(v,{size:"sm",className:"mr-2"}),"Running Diagnostics..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Run Diagnostics"]})}),e.jsx(o,{onClick:k,disabled:u||!m,variant:m?"default":"outline",className:"flex items-center",children:u?e.jsxs(e.Fragment,{children:[e.jsx(v,{size:"sm",className:"mr-2"}),"Fixing Access..."]}):e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"h-4 w-4 mr-2"}),"Fix Admin Access"]})})]}),!m&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(F,{className:"h-5 w-5 mr-2"}),"Admin fix is only <NAME_EMAIL>"]})}),C&&S&&e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center",children:[e.jsx(V,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:"Admin access fixed successfully! Try accessing the admin dashboard now."})]})]}):e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(F,{className:"h-5 w-5 mr-2"}),"You must be signed in to run diagnostics"]})}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Diagnostic Logs:"}),e.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:y.length>0?y.map((i,a)=>e.jsx("div",{className:`py-1 ${i.startsWith("ERROR")?"text-red-600":"text-gray-700"}`,children:i},a)):e.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet. Run diagnostics to see logs."})})]}),e.jsxs("div",{className:"flex justify-center mt-8",children:[e.jsx(A,{to:"/",children:e.jsx(o,{variant:"outline",className:"mr-4",children:"Return to Home"})}),e.jsx(A,{to:"/admin",children:e.jsx(o,{children:"Try Admin Dashboard"})})]})]})}),e.jsx(H,{})]})};export{G as default};
