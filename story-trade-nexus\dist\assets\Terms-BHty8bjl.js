import{j as e,M as t}from"./index-CrTHSN9_.js";const s=()=>e.jsx(t,{children:e.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-navy-800 font-playfair mb-6",children:"Terms of Service"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Effective Date: 23-05-2025"}),e.jsxs("div",{className:"prose prose-burgundy max-w-none",children:[e.jsx("p",{className:"mb-6",children:'Welcome to PeerBooks. These Terms of Service ("Terms") govern your use of our website, mobile application, and services (collectively, the "Service"). By using PeerBooks, you agree to these Terms. If you do not agree to these Terms, please do not use the Service.'}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"1. Acceptance of Terms"}),e.jsx("p",{children:"By accessing or using the Service, you agree to be bound by these Terms. If you are using the Service on behalf of an organization, you are agreeing to these Terms for that organization and promising that you have the authority to bind that organization to these Terms."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"2. Description of Service"}),e.jsx("p",{children:"PeerBooks is a peer-to-peer platform that allows users to rent, buy, and exchange used books directly with each other. We provide the platform to connect users but are not directly involved in transactions between users."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"3. User Accounts"}),e.jsx("p",{children:"To use certain features of the Service, you must register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete."}),e.jsx("p",{children:"You are responsible for safeguarding your password and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"4. User Content"}),e.jsx("p",{children:'You retain ownership of any content you submit to the Service, including book listings, reviews, and comments ("User Content"). By submitting User Content, you grant us a worldwide, non-exclusive, royalty-free license to use, copy, modify, and display your User Content in connection with the Service.'}),e.jsx("p",{children:"You are solely responsible for your User Content and the consequences of posting it. You represent and warrant that you own or have the necessary rights to post your User Content, and that your User Content does not violate the rights of any third party."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"5. Prohibited Conduct"}),e.jsx("p",{children:"You agree not to:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-4",children:[e.jsx("li",{children:"Use the Service for any illegal purpose or in violation of any laws"}),e.jsx("li",{children:"Post false, misleading, or deceptive content"}),e.jsx("li",{children:"Impersonate any person or entity"}),e.jsx("li",{children:"Harass, abuse, or harm another person"}),e.jsx("li",{children:"Interfere with or disrupt the Service"}),e.jsx("li",{children:"Attempt to gain unauthorized access to the Service"}),e.jsx("li",{children:"Use the Service to send spam or unsolicited messages"})]}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"6. Transactions Between Users"}),e.jsx("p",{children:"PeerBooks is not responsible for transactions between users. We do not guarantee the quality, safety, or legality of items listed, the truth or accuracy of listings, or the ability of users to complete transactions."}),e.jsx("p",{children:"Users are solely responsible for all aspects of their interactions and transactions with other users."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"7. Termination"}),e.jsx("p",{children:"We may terminate or suspend your account and access to the Service at our sole discretion, without notice, for conduct that we believe violates these Terms or is harmful to other users, us, or third parties, or for any other reason."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"8. Disclaimer of Warranties"}),e.jsx("p",{children:'THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.'}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"9. Limitation of Liability"}),e.jsx("p",{children:"IN NO EVENT SHALL PEERBOOKS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE SERVICE."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"10. Changes to Terms"}),e.jsx("p",{children:"We may modify these Terms at any time. If we make changes, we will provide notice of such changes, such as by sending an email, providing a notice through the Service, or updating the date at the top of these Terms. Your continued use of the Service following the posting of revised Terms means that you accept and agree to the changes."}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"11. Contact Information"}),e.jsxs("p",{children:["If you have any questions about these Terms, please contact us at: ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})]})]})]})})});export{s as default};
