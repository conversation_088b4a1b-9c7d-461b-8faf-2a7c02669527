import{f as D,j as e,a$ as Y,G as _,r as u,u as Q,J as N,D as U,H as V,S as J,I as q,a8 as S,h as C,v as I,E as v,a6 as K,aQ as W,L as z,y as X}from"./index-D8cEQRhY.js";import{S as Z,a as ee,b as se,c as te,d as re}from"./select-HUJnE3gw.js";import"./index-DxcDq1xx.js";import"./chevron-up-DySqBo_4.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=D("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),$=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],y=s=>!s.availability.includes("Sale")||!s.price?null:s.price,w=s=>{var r;if(!s.availability.includes("Rent")||!s.rentalPrice)return null;const o=s.rentalPrice,a=((r=s.rentalPeriod)==null?void 0:r.toLowerCase())||"per day";return a.includes("week")?o/7:a.includes("month")?o/30:a.includes("year")?o/365:o},c=s=>(s.createdAt instanceof Date?s.createdAt:new Date(s.createdAt)).getTime(),ne=(s,o,a)=>!s||s.length===0?[]:[...s].sort((r,t)=>{if(o!=="distance"){const l=a&&r.ownerCommunity&&r.ownerCommunity===a,i=a&&t.ownerCommunity&&t.ownerCommunity===a;if(l&&!i)return-1;if(i&&!l)return 1}switch(o){case"community-distance":return r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:r.distance!==void 0?-1:t.distance!==void 0?1:c(t)-c(r);case"price-low-high":{const l=y(r),i=y(t);return l!==null&&i!==null?l-i:l!==null?-1:i!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"price-high-low":{const l=y(r),i=y(t);return l!==null&&i!==null?i-l:l!==null?-1:i!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"rental-low-high":{const l=w(r),i=w(t);return l!==null&&i!==null?l-i:l!==null?-1:i!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"rental-high-low":{const l=w(r),i=w(t);return l!==null&&i!==null?i-l:l!==null?-1:i!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"distance":return r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:r.distance!==void 0?-1:t.distance!==void 0?1:c(t)-c(r);case"newest-first":return c(t)-c(r);case"oldest-first":return c(r)-c(t);default:return c(t)-c(r)}}),O=()=>"community-distance",le=(s,o)=>{switch(o){case"price-low-high":case"price-high-low":return s.some(a=>y(a)!==null);case"rental-low-high":case"rental-high-low":return s.some(a=>w(a)!==null);case"distance":case"community-distance":return s.some(a=>a.distance!==void 0);default:return!0}},ae=s=>$.filter(o=>le(s,o.value)),oe=({sortCriteria:s,onSortChange:o,books:a,disabled:r=!1,className:t=""})=>{const l=ae(a),i=$.find(d=>d.value===s),g=d=>{switch(d){case"price-low-high":case"price-high-low":return a.filter(m=>m.availability.includes("Sale")&&m.price).length;case"rental-low-high":case"rental-high-low":return a.filter(m=>m.availability.includes("Rent")&&m.rentalPrice).length;case"distance":case"community-distance":return a.filter(m=>m.distance!==void 0).length;default:return a.length}};return e.jsxs("div",{className:`flex flex-col gap-2 ${t}`,children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ie,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Sort by:"})]}),e.jsxs(Z,{value:s,onValueChange:d=>o(d),disabled:r,children:[e.jsx(ee,{className:"w-full",children:e.jsx(se,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx("span",{children:(i==null?void 0:i.label)||"Select sorting"}),i&&e.jsx(Y,{variant:"secondary",className:"ml-2 bg-burgundy-100 text-burgundy-700 hover:bg-burgundy-200",children:"Active"})]})})}),e.jsx(te,{children:l.map(d=>{const m=g(d.value),x=d.value===s;return e.jsx(re,{value:d.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-center justify-between w-full",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:d.label}),x&&e.jsx(_,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:d.description}),m<a.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[m," of ",a.length," books have this data"]})]})})},d.value)})})]}),i&&e.jsxs("div",{className:"text-xs text-gray-600 bg-gray-50 p-2 rounded border",children:[e.jsx("span",{className:"font-medium",children:"Current sort:"})," ",i.description,g(s)<a.length&&e.jsxs("span",{className:"block mt-1 text-blue-600",children:["Showing ",g(s)," of ",a.length," books with relevant data"]})]})]})},T="peerbooks-sort-preference",ce=()=>{const[s,o]=u.useState(O());u.useEffect(()=>{try{const l=localStorage.getItem(T);if(l){const i=l;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(i)&&o(i)}}catch(l){console.warn("Failed to load sort preference from localStorage:",l)}},[]);const a=u.useCallback(l=>{o(l);try{localStorage.setItem(T,l)}catch(i){console.warn("Failed to save sort preference to localStorage:",i)}},[]),r=u.useCallback((l,i)=>ne(l,s,i),[s]),t=u.useCallback(()=>{const l=O();a(l)},[a]);return{sortCriteria:s,setSortCriteria:a,sortBooks:r,resetSort:t}},fe=()=>{const{userData:s}=Q(),{sortCriteria:o,setSortCriteria:a,sortBooks:r}=ce(),[t,l]=u.useState(""),[i,g]=u.useState("All"),[d,m]=u.useState("All"),[x,G]=u.useState([]),[j,A]=u.useState(!0),[F,k]=u.useState(null),[b,p]=u.useState(null),H=["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"],M=["All","For Rent","For Sale","For Exchange"];u.useEffect(()=>{L()},[]);const L=async()=>{try{A(!0),k(null),p("loading"),console.log("BrowseBooks: Fetching books from Firebase"),N.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const n=s==null?void 0:s.community;console.log("BrowseBooks: User community for sorting:",n);const h=await U(!1,n);if(h.some(f=>f.distance!==void 0)){p("success");const f=n?`Books sorted by community (${n} first) and distance`:"Books sorted by distance (closest first)";N.success(f,{id:"location-toast",duration:4e3})}else{p("error");const f=n?`Books sorted by community (${n} first) and newest first`:"Books sorted by newest first";N.info(f,{id:"location-toast",duration:3e3})}G(h),console.log(`BrowseBooks: Fetched ${h.length} books from Firebase`)}catch(n){console.error("Error fetching books:",n),p("error"),n instanceof Error?(k(`Failed to load books: ${n.message}. Please try again.`),(n.message.includes("permission")||n.message.includes("denied"))&&(p("denied"),N.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):k("Failed to load books. Please try again.")}finally{A(!1)}},R=()=>{console.log("BrowseBooks: Refreshing books"),L()},P=x.filter(n=>{const h=t===""||n.title.toLowerCase().includes(t.toLowerCase())||n.author.toLowerCase().includes(t.toLowerCase()),E=i==="All"||n.genre.includes(i),f=d==="All"||n.availability.includes(d);return h&&E&&f}),B=r(P,s==null?void 0:s.community);return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(V,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(q,{type:"text",placeholder:"Search by title or author...",className:"pl-10",value:t,onChange:n=>l(n.target.value)})]}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:i,onChange:n=>g(n.target.value),children:H.map(n=>e.jsx("option",{value:n,children:n},n))}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:d,onChange:n=>m(n.target.value),children:M.map(n=>e.jsx("option",{value:n,children:n},n))}),e.jsx(oe,{sortCriteria:o,onSortChange:a,books:P,disabled:j})]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsxs("span",{className:"font-medium",children:["Showing ",B.length," of ",x.length," books"]}),(t||i!=="All"||d!=="All")&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[b==="loading"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),b==="success"&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and distance`:"Books sorted by distance (closest first)"})]}),b==="error"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and newest first`:"Books sorted by newest first"})]}),b==="denied"&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(C,{variant:"outline",onClick:R,disabled:j,className:"text-sm",children:j?e.jsxs(e.Fragment,{children:[e.jsx(I,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),F&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:F}),e.jsx(C,{variant:"link",onClick:R,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),j?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((n,h)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(v,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(v,{className:"h-6 w-3/4 mb-2"}),e.jsx(v,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(v,{className:"h-8 w-20"}),e.jsx(v,{className:"h-8 w-20"})]})]})]},h))}):B.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:B.map(n=>e.jsx(K,{book:n},n.id))}):x.length===0?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(W,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(z,{to:"/add-books",children:e.jsx(C,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(X,{})]})};export{fe as default};
