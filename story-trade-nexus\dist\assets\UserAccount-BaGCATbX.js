import{f as ee,u as se,r,s as ae,Z as te,B as T,j as e,M as P,E as c,L as f,h as o,$ as le,a0 as ie,a1 as ne,a2 as re,a3 as B,c as oe,a4 as de,a5 as ce,a6 as U,X as me,k as xe,I as m,a7 as he,a8 as ue,a9 as ge,aa as Y,g as pe,J as v,ab as ye,ac as je}from"./index-beYRG-mR.js";import{P as k}from"./plus-D5vwbLwv.js";import{S as Ne}from"./square-pen-DV9jIHLh.js";import{S as fe}from"./save-BgXYuefX.js";import{P as ve}from"./phone-CwN_8sIa.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=ee("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),Ae=()=>{var I;const{currentUser:l,userData:s,refreshUserData:$,signOut:z}=se(),[u,R]=r.useState([]),[J,A]=r.useState(!0),[b,C]=r.useState(!1),[x,g]=r.useState("dashboard"),p=ae(),E=te(),[i,w]=r.useState({displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}),[we,y]=r.useState([]),[M,j]=r.useState([]),[F,D]=r.useState(!1),[L,O]=r.useState(null);r.useEffect(()=>{const a=E.pathname;a.includes("profile")?g("profile"):a.includes("books")?g("books"):a.includes("settings")?g("settings"):g("dashboard")},[E.pathname]),r.useEffect(()=>{(async()=>{if(l)try{A(!0);const t=await pe(l.uid);R(t),s&&(w({displayName:s.displayName||"",phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||"",community:s.community||"",customCommunity:""}),s.pincode&&s.pincode.length===6&&S(s.pincode))}catch(t){console.error("Error fetching user data:",t),v.error("Failed to load profile data")}finally{A(!1)}})()},[l,s]);const Z=a=>{if(!a)return"N/A";const t=a.toDate?a.toDate():new Date(a);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(t)},S=async a=>{if(a.length!==6){y([]),j([]);return}D(!0),O(null);try{const t=await ye(a);y(t);const n=t.map(h=>({value:h,label:h}));n.push({value:"Other",label:"Other (specify below)"}),j(n),console.log(`Profile: Found ${t.length} communities for pincode ${a}`),console.log("Profile: Community options created:",n)}catch(t){console.error("Error fetching communities:",t),O("Failed to load communities for this pincode"),y([]),j([])}finally{D(!1)}},d=a=>{const{name:t,value:n}=a.target;w(h=>({...h,[t]:n})),t==="pincode"&&n.length===6?S(n):t==="pincode"&&n.length!==6&&(y([]),j([]))},q=a=>{console.log("Profile: Community selection changed to:",a),w(t=>{const n={...t,community:a,customCommunity:a==="Other"?t.customCommunity:""};return console.log("Profile: Updated form data:",n),n})},H=async a=>{if(a.preventDefault(),!!l)try{const t={...i,community:i.community==="Other"?i.customCommunity:i.community},{customCommunity:n,...h}=t;console.log("Profile: Form data before save:",i),console.log("Profile: Final data to save:",h),console.log("Profile: Community value being saved:",h.community),await je(l.uid,h),await $(),v.success("Profile updated successfully"),C(!1)}catch(t){console.error("Error updating profile:",t),v.error("Failed to update profile")}},V=()=>{s&&(w({displayName:s.displayName||"",phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||"",community:s.community||"",customCommunity:""}),s.pincode&&s.pincode.length===6?S(s.pincode):(y([]),j([]))),C(!1)},N=a=>{switch(g(a),a){case"profile":p("/profile");break;case"books":p("/my-books");break;case"settings":p("/settings");break;default:p("/dashboard")}},W=async()=>{try{await z(),p("/"),v.success("Signed out successfully")}catch(a){console.error("Error signing out:",a),v.error("Failed to sign out")}},X=()=>{var a;return s!=null&&s.displayName?s.displayName.split(" ").map(t=>t[0]).join("").toUpperCase().substring(0,2):((a=l==null?void 0:l.email)==null?void 0:a.substring(0,2).toUpperCase())||"U"},G=(s==null?void 0:s.displayName)||(l==null?void 0:l.displayName)||((I=l==null?void 0:l.email)==null?void 0:I.split("@")[0])||"Reader";s!=null&&s.email||l!=null&&l.email;const K=u.length||0,Q=u.filter(a=>a.approvalStatus===T.Approved||!a.approvalStatus).length,_=u.filter(a=>a.approvalStatus===T.Pending).length;return J?e.jsx(P,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsxs("div",{className:"md:w-1/4",children:[e.jsx(c,{className:"h-40 w-40 rounded-full mx-auto"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx(c,{className:"h-6 w-full"}),e.jsx(c,{className:"h-4 w-3/4"}),e.jsx(c,{className:"h-4 w-1/2"})]})]}),e.jsxs("div",{className:"md:w-3/4 space-y-6",children:[e.jsx(c,{className:"h-8 w-1/2"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(c,{className:"h-20 w-full"}),e.jsx(c,{className:"h-20 w-full"}),e.jsx(c,{className:"h-20 w-full"}),e.jsx(c,{className:"h-20 w-full"})]})]})]})})})}):!l||!s?e.jsx(P,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:"User Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Please sign in to view your account."}),e.jsx(f,{to:"/signin",children:e.jsx(o,{children:"Sign In"})})]})})}):e.jsx(P,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsxs("div",{className:"md:w-64 bg-gray-50 p-6 border-r border-gray-200",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[e.jsxs(le,{className:"h-20 w-20",children:[e.jsx(ie,{src:s.photoURL||"",alt:s.displayName||"User"}),e.jsx(ne,{className:"text-xl bg-burgundy-100 text-burgundy-700",children:X()})]}),e.jsx("h2",{className:"mt-4 text-lg font-semibold text-center",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-600 text-center",children:s.email})]}),e.jsxs("nav",{className:"space-y-1",children:[e.jsxs("button",{onClick:()=>N("dashboard"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${x==="dashboard"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(re,{className:"h-4 w-4 mr-3"}),"Dashboard"]}),e.jsxs("button",{onClick:()=>N("profile"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${x==="profile"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(B,{className:"h-4 w-4 mr-3"}),"Profile"]}),e.jsxs("button",{onClick:()=>N("books"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${x==="books"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(oe,{className:"h-4 w-4 mr-3"}),"My Books"]}),e.jsxs("button",{onClick:()=>N("settings"),className:`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${x==="settings"?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"}`,children:[e.jsx(de,{className:"h-4 w-4 mr-3"}),"Settings"]})]}),e.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200 mt-6",children:e.jsxs("button",{onClick:W,className:"w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[e.jsx(ce,{className:"h-4 w-4 mr-3"}),"Sign Out"]})})]}),e.jsxs("div",{className:"flex-1 p-6",children:[x==="dashboard"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:["Welcome, ",G,"!"]}),e.jsx("p",{className:"text-gray-600",children:"Manage your books and exchanges"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(f,{to:"/add-books",children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(k,{className:"h-4 w-4"}),"Add New Books"]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-burgundy-600",children:K}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Books"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:Q}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-amber-600",children:_}),e.jsx("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold text-navy-800",children:"Your Books"}),e.jsx(o,{variant:"link",className:"text-burgundy-600",onClick:()=>N("books"),children:"View All"})]}),u.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:u.slice(0,4).map(a=>e.jsx(U,{book:a},a.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(f,{to:"/add-books",children:e.jsxs(o,{children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]})]}),x==="profile"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Profile"}),b?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(o,{variant:"outline",onClick:V,className:"flex items-center gap-2",children:[e.jsx(me,{className:"h-4 w-4"}),"Cancel"]}),e.jsxs(o,{onClick:H,className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-4 w-4"}),"Save Changes"]})]}):e.jsxs(o,{onClick:()=>C(!0),className:"flex items-center gap-2",children:[e.jsx(Ne,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(B,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(xe,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:s.email})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(ve,{className:"h-4 w-4 text-gray-400 mr-2"}),b?e.jsx(m,{name:"phone",value:i.phone,onChange:d,placeholder:"Enter phone number"}):e.jsx("p",{children:s.phone||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(he,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:Z(s.createdAt)})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Display Name"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(B,{className:"h-4 w-4 text-gray-400 mr-2"}),b?e.jsx(m,{name:"displayName",value:i.displayName,onChange:d,placeholder:"Enter display name"}):e.jsx("p",{children:s.displayName||"Not provided"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(ue,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Address Information"]}),b?e.jsxs("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Address"}),e.jsx(m,{name:"address",value:i.address,onChange:d,placeholder:"Enter your address",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"City"}),e.jsx(m,{name:"city",value:i.city,onChange:d,placeholder:"Enter city",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"State"}),e.jsx(m,{name:"state",value:i.state,onChange:d,placeholder:"Enter state",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx(m,{name:"pincode",value:i.pincode,onChange:d,placeholder:"Enter pincode",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm text-gray-500",children:["Community",e.jsx("span",{className:"text-xs text-blue-600 ml-1",children:"(affects book discovery)"})]}),M.length>0?e.jsxs("div",{className:"mt-1",children:[e.jsx(ge,{options:M,value:i.community,onChange:q,placeholder:"Select your community",emptyMessage:"No communities found",disabled:F}),F&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Loading communities..."}),L&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:L}),i.community==="Other"&&e.jsx(m,{name:"customCommunity",value:i.customCommunity,onChange:d,placeholder:"Enter your community name",className:"mt-2"})]}):e.jsxs("div",{className:"mt-1",children:[e.jsx(m,{name:"community",value:i.community,onChange:d,placeholder:"Enter your community name",className:"mt-1"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pincode above to see available communities"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsx(m,{name:"apartment",value:i.apartment,onChange:d,placeholder:"Enter apartment or building name",className:"mt-1"})]})]}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(Y,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:s.address||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"City"}),e.jsx("p",{className:"mt-1",children:s.city||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"State"}),e.jsx("p",{className:"mt-1",children:s.state||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx("p",{className:"mt-1",children:s.pincode||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Community"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(Y,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsxs("div",{children:[e.jsx("p",{children:s.community||"Not provided"}),s.community&&e.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"Books from your community appear first in Browse Books"})]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(be,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:s.apartment||"Not provided"})]})]})]})]})]}),x==="books"&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Books"}),e.jsx(f,{to:"/add-books",children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(k,{className:"h-4 w-4"}),"Add New Book"]})})]}),u.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:u.map(a=>e.jsx(U,{book:a},a.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(f,{to:"/add-books",children:e.jsxs(o,{children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]}),x==="settings"&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-6",children:"Account Settings"}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Notification Preferences"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to customize your notification preferences here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Privacy Settings"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to manage your privacy settings here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-red-600",children:"Danger Zone"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"These actions are irreversible. Please proceed with caution."}),e.jsx(o,{variant:"destructive",disabled:!0,children:"Delete Account"})]})]})]})]})})})})};export{Ae as default};
