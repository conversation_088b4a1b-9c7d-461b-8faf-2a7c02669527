import{u as i,j as s,L as e,i as a}from"./index-pJ8lwbxh.js";import{A as n}from"./AdminLayout-B8-5mL2q.js";import"./users-Buba-DeM.js";const o=()=>{const{currentUser:d}=i();return s.jsxs(n,{children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Dashboard"}),s.jsxs("p",{className:"text-gray-600 mb-4",children:["Welcome back, ",(d==null?void 0:d.displayName)||"Admin",". Manage your book-sharing platform from here."]}),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:s.jsx("p",{className:"text-blue-800 text-sm",children:"This is a restricted area. Please be careful when making changes to the system."})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Book Approvals"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Review and approve new book submissions from users."}),s.jsx(e,{to:"/admin/books",children:s.jsx(a,{children:"Manage Book Approvals"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"User Management"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Manage user accounts, roles, and permissions."}),s.jsx(e,{to:"/admin/users",children:s.jsx(a,{children:"Manage Users"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Contact Messages"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"View and respond to messages from users."}),s.jsx(e,{to:"/admin/messages",children:s.jsx(a,{children:"View Messages"})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Admin Tools"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Access administrative utilities and maintenance tools."}),s.jsx(e,{to:"/admin/utilities",children:s.jsx(a,{children:"Access Tools"})})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Admin Settings"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Configure admin preferences and system settings."}),s.jsx(e,{to:"/admin/settings",children:s.jsx(a,{children:"Configure Settings"})})]})]})]})};export{o as default};
