#!/usr/bin/env node

/**
 * WebP Migration Script for PeerBooks
 *
 * This script converts all existing book images in Firebase Storage from their current formats
 * (JPEG, PNG, etc.) to WebP format for better performance and smaller file sizes.
 *
 * Features:
 * - Batch processes all images in Firebase Storage
 * - Maintains existing image optimization standards (max 1200px width, preserve aspect ratio)
 * - Updates database references to reflect new file extensions
 * - Implements proper error handling and logging
 * - Creates backups for rollback capability
 * - Preserves existing file structure and naming conventions
 */

import { initializeFirebaseAdmin, getFirebaseServices, testFirebaseConnection } from './firebase-config.js';
import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  // Firebase project configuration
  projectId: 'book-share-98f6a',
  storageBucket: 'book-share-98f6a.firebasestorage.app',

  // Image processing settings
  maxWidth: 1200,
  webpQuality: 85,
  webpEffort: 6,

  // Batch processing settings
  batchSize: 10,
  delayBetweenBatches: 1000, // ms

  // Backup settings
  backupDir: path.join(__dirname, '../backups/webp-migration'),
  createBackups: true,

  // Logging
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  logFile: path.join(__dirname, '../logs/webp-migration.log')
};

// Firebase services (will be initialized later)
let storage, firestore;

// Logging utility
class Logger {
  constructor(logFile, logLevel = 'info') {
    this.logFile = logFile;
    this.logLevel = logLevel;
    this.levels = { debug: 0, info: 1, warn: 2, error: 3 };
  }

  async log(level, message, data = null) {
    if (this.levels[level] >= this.levels[this.logLevel]) {
      const timestamp = new Date().toISOString();
      const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

      console.log(logEntry);
      if (data) {
        console.log(JSON.stringify(data, null, 2));
      }

      // Write to log file
      try {
        await fs.mkdir(path.dirname(this.logFile), { recursive: true });
        await fs.appendFile(this.logFile, logEntry + (data ? '\n' + JSON.stringify(data, null, 2) : '') + '\n');
      } catch (error) {
        console.error('Failed to write to log file:', error);
      }
    }
  }

  debug(message, data) { return this.log('debug', message, data); }
  info(message, data) { return this.log('info', message, data); }
  warn(message, data) { return this.log('warn', message, data); }
  error(message, data) { return this.log('error', message, data); }
}

const logger = new Logger(CONFIG.logFile, CONFIG.logLevel);

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  try {
    logger.info('Initializing Firebase Admin SDK...');

    const services = await initializeFirebaseAdmin();
    storage = services.storage;
    firestore = services.firestore;

    // Test connection
    const connectionOk = await testFirebaseConnection();
    if (!connectionOk) {
      throw new Error('Firebase connection test failed');
    }

    logger.info('Firebase Admin SDK initialized and tested successfully');
    return true;
  } catch (error) {
    logger.error('Failed to initialize Firebase Admin SDK', { error: error.message });
    return false;
  }
}

// Get all image files from Firebase Storage
async function getAllImageFiles() {
  try {
    logger.info('Fetching all image files from Firebase Storage...');

    const bucket = storage.bucket();
    const [files] = await bucket.getFiles({
      prefix: 'book-images/',
    });

    // Filter for image files (exclude .webp files if any exist)
    const imageFiles = files.filter(file => {
      const fileName = file.name.toLowerCase();
      return (
        (fileName.endsWith('.jpg') ||
         fileName.endsWith('.jpeg') ||
         fileName.endsWith('.png') ||
         fileName.endsWith('.gif')) &&
        !fileName.endsWith('.webp')
      );
    });

    logger.info(`Found ${imageFiles.length} image files to convert`);
    return imageFiles;
  } catch (error) {
    logger.error('Failed to fetch image files', { error: error.message });
    throw error;
  }
}

// Convert image to WebP format
async function convertImageToWebP(imageBuffer, originalFileName) {
  try {
    logger.debug(`Converting image: ${originalFileName}`);

    const webpBuffer = await sharp(imageBuffer)
      .resize({
        width: CONFIG.maxWidth,
        height: undefined,
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({
        quality: CONFIG.webpQuality,
        effort: CONFIG.webpEffort
      })
      .toBuffer();

    logger.debug(`Converted ${originalFileName}: ${imageBuffer.length} bytes -> ${webpBuffer.length} bytes`);
    return webpBuffer;
  } catch (error) {
    logger.error(`Failed to convert image: ${originalFileName}`, { error: error.message });
    throw error;
  }
}

// Generate new WebP file path
function generateWebPPath(originalPath) {
  const pathParts = originalPath.split('.');
  pathParts[pathParts.length - 1] = 'webp';
  return pathParts.join('.');
}

// Create backup of original file
async function createBackup(file, backupDir) {
  if (!CONFIG.createBackups) return null;

  try {
    const backupPath = path.join(backupDir, file.name);
    await fs.mkdir(path.dirname(backupPath), { recursive: true });

    const [buffer] = await file.download();
    await fs.writeFile(backupPath, buffer);

    logger.debug(`Created backup: ${backupPath}`);
    return backupPath;
  } catch (error) {
    logger.warn(`Failed to create backup for ${file.name}`, { error: error.message });
    return null;
  }
}

// Process a single image file
async function processImageFile(file, backupDir) {
  const originalPath = file.name;
  const webpPath = generateWebPPath(originalPath);

  try {
    logger.info(`Processing: ${originalPath} -> ${webpPath}`);

    // Create backup
    const backupPath = await createBackup(file, backupDir);

    // Download original image
    const [imageBuffer] = await file.download();

    // Convert to WebP
    const webpBuffer = await convertImageToWebP(imageBuffer, originalPath);

    // Upload WebP version
    const bucket = storage.bucket();
    const webpFile = bucket.file(webpPath);

    await webpFile.save(webpBuffer, {
      metadata: {
        contentType: 'image/webp',
        metadata: {
          originalFormat: path.extname(originalPath).substring(1),
          convertedAt: new Date().toISOString(),
          originalSize: imageBuffer.length,
          webpSize: webpBuffer.length
        }
      }
    });

    logger.info(`Successfully uploaded WebP: ${webpPath}`);

    return {
      success: true,
      originalPath,
      webpPath,
      originalSize: imageBuffer.length,
      webpSize: webpBuffer.length,
      backupPath
    };
  } catch (error) {
    logger.error(`Failed to process ${originalPath}`, { error: error.message });
    return {
      success: false,
      originalPath,
      error: error.message
    };
  }
}

// Update database references
async function updateDatabaseReferences(conversionResults) {
  logger.info('Updating database references...');

  try {
    const booksCollection = firestore.collection('books');
    const booksSnapshot = await booksCollection.get();

    let updatedCount = 0;
    const batch = firestore.batch();

    for (const doc of booksSnapshot.docs) {
      const bookData = doc.data();
      let needsUpdate = false;
      const updates = {};

      // Update main imageUrl
      if (bookData.imageUrl) {
        const conversion = conversionResults.find(r =>
          r.success && bookData.imageUrl.includes(r.originalPath.split('/').pop())
        );
        if (conversion) {
          updates.imageUrl = bookData.imageUrl.replace(
            conversion.originalPath.split('/').pop(),
            conversion.webpPath.split('/').pop()
          );
          needsUpdate = true;
        }
      }

      // Update imageUrls array
      if (bookData.imageUrls && Array.isArray(bookData.imageUrls)) {
        const updatedImageUrls = bookData.imageUrls.map(url => {
          const conversion = conversionResults.find(r =>
            r.success && url.includes(r.originalPath.split('/').pop())
          );
          return conversion ?
            url.replace(
              conversion.originalPath.split('/').pop(),
              conversion.webpPath.split('/').pop()
            ) : url;
        });

        if (JSON.stringify(updatedImageUrls) !== JSON.stringify(bookData.imageUrls)) {
          updates.imageUrls = updatedImageUrls;
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        batch.update(doc.ref, updates);
        updatedCount++;
      }
    }

    if (updatedCount > 0) {
      await batch.commit();
      logger.info(`Updated ${updatedCount} book records in database`);
    } else {
      logger.info('No database updates needed');
    }

    return updatedCount;
  } catch (error) {
    logger.error('Failed to update database references', { error: error.message });
    throw error;
  }
}

// Clean up original files after successful conversion
async function cleanupOriginalFiles(conversionResults) {
  logger.info('Cleaning up original files...');

  const successfulConversions = conversionResults.filter(r => r.success);
  let deletedCount = 0;

  for (const conversion of successfulConversions) {
    try {
      const bucket = storage.bucket();
      const originalFile = bucket.file(conversion.originalPath);
      await originalFile.delete();

      logger.debug(`Deleted original file: ${conversion.originalPath}`);
      deletedCount++;
    } catch (error) {
      logger.warn(`Failed to delete original file: ${conversion.originalPath}`, { error: error.message });
    }
  }

  logger.info(`Deleted ${deletedCount} original files`);
  return deletedCount;
}

// Generate migration report
async function generateReport(conversionResults, startTime, updatedRecords, deletedFiles) {
  const endTime = new Date();
  const duration = endTime - startTime;

  const successful = conversionResults.filter(r => r.success);
  const failed = conversionResults.filter(r => !r.success);

  const totalOriginalSize = successful.reduce((sum, r) => sum + r.originalSize, 0);
  const totalWebpSize = successful.reduce((sum, r) => sum + r.webpSize, 0);
  const spaceSaved = totalOriginalSize - totalWebpSize;
  const compressionRatio = totalOriginalSize > 0 ? (spaceSaved / totalOriginalSize * 100).toFixed(2) : 0;

  const report = {
    migration: {
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: `${Math.round(duration / 1000)}s`,
    },
    results: {
      totalFiles: conversionResults.length,
      successful: successful.length,
      failed: failed.length,
      successRate: `${((successful.length / conversionResults.length) * 100).toFixed(2)}%`
    },
    storage: {
      originalSize: `${(totalOriginalSize / 1024 / 1024).toFixed(2)} MB`,
      webpSize: `${(totalWebpSize / 1024 / 1024).toFixed(2)} MB`,
      spaceSaved: `${(spaceSaved / 1024 / 1024).toFixed(2)} MB`,
      compressionRatio: `${compressionRatio}%`
    },
    database: {
      recordsUpdated: updatedRecords
    },
    cleanup: {
      originalFilesDeleted: deletedFiles
    },
    failures: failed.map(f => ({
      file: f.originalPath,
      error: f.error
    }))
  };

  // Save report to file
  const reportPath = path.join(CONFIG.backupDir, 'migration-report.json');
  await fs.mkdir(path.dirname(reportPath), { recursive: true });
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

  logger.info('Migration Report:', report);
  logger.info(`Full report saved to: ${reportPath}`);

  return report;
}

// Main migration function
async function runMigration() {
  const startTime = new Date();
  console.log('🔄 runMigration() called');
  logger.info('Starting WebP migration process...');

  try {
    console.log('🔧 Initializing Firebase...');
    // Initialize Firebase
    const initialized = await initializeFirebase();
    if (!initialized) {
      throw new Error('Failed to initialize Firebase');
    }
    console.log('✅ Firebase initialized successfully');

    // Create backup directory
    if (CONFIG.createBackups) {
      await fs.mkdir(CONFIG.backupDir, { recursive: true });
      logger.info(`Backup directory created: ${CONFIG.backupDir}`);
    }

    // Get all image files
    const imageFiles = await getAllImageFiles();

    if (imageFiles.length === 0) {
      logger.info('No images found to convert. Migration complete.');
      return;
    }

    // Process images in batches
    const conversionResults = [];
    const totalBatches = Math.ceil(imageFiles.length / CONFIG.batchSize);

    for (let i = 0; i < totalBatches; i++) {
      const batchStart = i * CONFIG.batchSize;
      const batchEnd = Math.min(batchStart + CONFIG.batchSize, imageFiles.length);
      const batch = imageFiles.slice(batchStart, batchEnd);

      logger.info(`Processing batch ${i + 1}/${totalBatches} (${batch.length} files)`);

      // Process batch
      const batchPromises = batch.map(file => processImageFile(file, CONFIG.backupDir));
      const batchResults = await Promise.all(batchPromises);
      conversionResults.push(...batchResults);

      // Log batch progress
      const batchSuccessful = batchResults.filter(r => r.success).length;
      logger.info(`Batch ${i + 1} complete: ${batchSuccessful}/${batch.length} successful`);

      // Delay between batches to avoid rate limiting
      if (i < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenBatches));
      }
    }

    // Update database references
    const updatedRecords = await updateDatabaseReferences(conversionResults);

    // Clean up original files
    const deletedFiles = await cleanupOriginalFiles(conversionResults);

    // Generate and save report
    const report = await generateReport(conversionResults, startTime, updatedRecords, deletedFiles);

    logger.info('WebP migration completed successfully!');
    return report;

  } catch (error) {
    logger.error('Migration failed', { error: error.message, stack: error.stack });
    throw error;
  }
}

// Rollback function (in case of issues)
async function rollbackMigration(reportPath) {
  logger.info('Starting rollback process...');

  try {
    // Read migration report
    const reportContent = await fs.readFile(reportPath, 'utf8');
    const report = JSON.parse(reportContent);

    // TODO: Implement rollback logic
    // This would involve:
    // 1. Restoring original files from backup
    // 2. Reverting database changes
    // 3. Deleting WebP files

    logger.warn('Rollback functionality not yet implemented');
    logger.info('To manually rollback:');
    logger.info('1. Restore files from backup directory');
    logger.info('2. Update database references back to original extensions');
    logger.info('3. Delete WebP files from storage');

  } catch (error) {
    logger.error('Rollback failed', { error: error.message });
    throw error;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log('🚀 WebP Migration Script Starting...');
  console.log('📍 Command:', command);
  console.log('📍 Arguments:', args);

  try {
    switch (command) {
      case 'migrate':
        console.log('🔄 Starting migration process...');
        await runMigration();
        console.log('✅ Migration completed successfully!');
        break;
      case 'rollback':
        const reportPath = args[1];
        if (!reportPath) {
          console.error('Usage: node webp-migration.js rollback <report-path>');
          process.exit(1);
        }
        console.log('🔄 Starting rollback process...');
        await rollbackMigration(reportPath);
        console.log('✅ Rollback completed successfully!');
        break;
      default:
        console.log('Usage:');
        console.log('  node webp-migration.js migrate     - Run WebP migration');
        console.log('  node webp-migration.js rollback <report-path> - Rollback migration');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run if called directly
const currentFileUrl = import.meta.url;
const scriptPath = `file:///${process.argv[1].replace(/\\/g, '/')}`;

console.log('🔍 Debug: currentFileUrl =', currentFileUrl);
console.log('🔍 Debug: scriptPath =', scriptPath);
console.log('🔍 Debug: process.argv[1] =', process.argv[1]);

if (currentFileUrl === scriptPath || process.argv[1].endsWith('webp-migration.js')) {
  console.log('✅ Script called directly, running main()');
  main();
} else {
  console.log('ℹ️ Script imported as module, not running main()');
}
