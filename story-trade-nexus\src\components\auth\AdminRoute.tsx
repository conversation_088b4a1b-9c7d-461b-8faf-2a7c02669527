import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/lib/AuthContext';
import { Spinner } from '@/components/ui/spinner';

interface AdminRouteProps {
  children: React.ReactNode;
}

/**
 * AdminRoute component - Simplified version
 * Protects routes that should only be accessible by admin users
 */
const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const { currentUser, isAdmin, loading } = useAuth();
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    // Give the auth context time to initialize
    if (!loading) {
      const timer = setTimeout(() => {
        setInitializing(false);
      }, 500); // Small delay to ensure auth state is stable

      return () => clearTimeout(timer);
    }
  }, [loading]);

  // Show loading spinner while authentication is initializing
  if (loading || initializing) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading admin portal...</p>
        </div>
      </div>
    );
  }

  // If user is not logged in, redirect to login page
  if (!currentUser) {
    console.log('AdminRoute: No user logged in, redirecting to signin');
    return <Navigate to="/signin" replace />;
  }

  // Special <NAME_EMAIL> - always grant access
  if (currentUser.email === '<EMAIL>') {
    console.log('AdminRoute: Granting access to special admin user');
    return <>{children}</>;
  }

  // Check if user is admin
  if (!isAdmin) {
    console.log('AdminRoute: User is not admin, redirecting to unauthorized');
    return <Navigate to="/unauthorized" replace />;
  }

  // If user is logged in and is an admin, render the protected route
  console.log('AdminRoute: Access granted to admin user');
  return <>{children}</>;
};

export default AdminRoute;
