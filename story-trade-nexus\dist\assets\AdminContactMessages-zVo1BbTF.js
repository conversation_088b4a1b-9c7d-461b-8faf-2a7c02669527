import{u as J,r as j,ay as O,j as s,a$ as A,h as T,ax as C,v as U,aL as z,k as K}from"./index-D8cEQRhY.js";import{g as Z,m as ee,a as te}from"./contactMessageService-BIpFzvkt.js";import{D as ne,a as ae,b as re,c as se,d as ie}from"./dialog-MnCAt3TN.js";import{A as oe}from"./AdminLayout-DDl0V-G9.js";import{P as ce}from"./phone-Dj4TPzZL.js";import{E as F}from"./eye-off-D8hRYQ_b.js";import{E as R}from"./eye-DIECuMxU.js";import"./users-u5r6PhU6.js";function x(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function P(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}const G=6048e5,de=864e5;let ue={};function E(){return ue}function S(e,t){var u,l,m,f;const n=E(),a=(t==null?void 0:t.weekStartsOn)??((l=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:l.weekStartsOn)??n.weekStartsOn??((f=(m=n.locale)==null?void 0:m.options)==null?void 0:f.weekStartsOn)??0,r=x(e),i=r.getDay(),d=(i<a?7:0)+i-a;return r.setDate(r.getDate()-d),r.setHours(0,0,0,0),r}function Y(e){return S(e,{weekStartsOn:1})}function V(e){const t=x(e),n=t.getFullYear(),a=P(e,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);const r=Y(a),i=P(e,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);const d=Y(i);return t.getTime()>=r.getTime()?n+1:t.getTime()>=d.getTime()?n:n-1}function L(e){const t=x(e);return t.setHours(0,0,0,0),t}function H(e){const t=x(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function le(e,t){const n=L(e),a=L(t),r=+n-H(n),i=+a-H(a);return Math.round((r-i)/de)}function me(e){const t=V(e),n=P(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),Y(n)}function he(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function fe(e){if(!he(e)&&typeof e!="number")return!1;const t=x(e);return!isNaN(Number(t))}function ge(e){const t=x(e),n=P(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}const we={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ye=(e,t,n)=>{let a;const r=we[e];return typeof r=="string"?a=r:t===1?a=r.one:a=r.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a};function q(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const xe={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},be={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},pe={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},ve={date:q({formats:xe,defaultWidth:"full"}),time:q({formats:be,defaultWidth:"full"}),dateTime:q({formats:pe,defaultWidth:"full"})},Me={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Pe=(e,t,n,a)=>Me[e];function D(e){return(t,n)=>{const a=n!=null&&n.context?String(n.context):"standalone";let r;if(a==="formatting"&&e.formattingValues){const d=e.defaultFormattingWidth||e.defaultWidth,u=n!=null&&n.width?String(n.width):d;r=e.formattingValues[u]||e.formattingValues[d]}else{const d=e.defaultWidth,u=n!=null&&n.width?String(n.width):e.defaultWidth;r=e.values[u]||e.values[d]}const i=e.argumentCallback?e.argumentCallback(t):t;return r[i]}}const ke={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},je={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Ne={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Oe={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},De={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},We={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Se=(e,t)=>{const n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},Te={ordinalNumber:Se,era:D({values:ke,defaultWidth:"wide"}),quarter:D({values:je,defaultWidth:"wide",argumentCallback:e=>e-1}),month:D({values:Ne,defaultWidth:"wide"}),day:D({values:Oe,defaultWidth:"wide"}),dayPeriod:D({values:De,defaultWidth:"wide",formattingValues:We,defaultFormattingWidth:"wide"})};function W(e){return(t,n={})=>{const a=n.width,r=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(r);if(!i)return null;const d=i[0],u=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(u)?Ee(u,y=>y.test(d)):Ye(u,y=>y.test(d));let m;m=e.valueCallback?e.valueCallback(l):l,m=n.valueCallback?n.valueCallback(m):m;const f=t.slice(d.length);return{value:m,rest:f}}}function Ye(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function Ee(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function Ce(e){return(t,n={})=>{const a=t.match(e.matchPattern);if(!a)return null;const r=a[0],i=t.match(e.parsePattern);if(!i)return null;let d=e.valueCallback?e.valueCallback(i[0]):i[0];d=n.valueCallback?n.valueCallback(d):d;const u=t.slice(r.length);return{value:d,rest:u}}}const Fe=/^(\d+)(th|st|nd|rd)?/i,Re=/\d+/i,qe={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Ae={any:[/^b/i,/^(a|c)/i]},Le={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},He={any:[/1/i,/2/i,/3/i,/4/i]},Qe={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Xe={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},_e={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Be={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Ge={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ve={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},$e={ordinalNumber:Ce({matchPattern:Fe,parsePattern:Re,valueCallback:e=>parseInt(e,10)}),era:W({matchPatterns:qe,defaultMatchWidth:"wide",parsePatterns:Ae,defaultParseWidth:"any"}),quarter:W({matchPatterns:Le,defaultMatchWidth:"wide",parsePatterns:He,defaultParseWidth:"any",valueCallback:e=>e+1}),month:W({matchPatterns:Qe,defaultMatchWidth:"wide",parsePatterns:Xe,defaultParseWidth:"any"}),day:W({matchPatterns:_e,defaultMatchWidth:"wide",parsePatterns:Be,defaultParseWidth:"any"}),dayPeriod:W({matchPatterns:Ge,defaultMatchWidth:"any",parsePatterns:Ve,defaultParseWidth:"any"})},Ie={code:"en-US",formatDistance:ye,formatLong:ve,formatRelative:Pe,localize:Te,match:$e,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Je(e){const t=x(e);return le(t,ge(t))+1}function Ue(e){const t=x(e),n=+Y(t)-+me(t);return Math.round(n/G)+1}function $(e,t){var f,y,b,v;const n=x(e),a=n.getFullYear(),r=E(),i=(t==null?void 0:t.firstWeekContainsDate)??((y=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:y.firstWeekContainsDate)??r.firstWeekContainsDate??((v=(b=r.locale)==null?void 0:b.options)==null?void 0:v.firstWeekContainsDate)??1,d=P(e,0);d.setFullYear(a+1,0,i),d.setHours(0,0,0,0);const u=S(d,t),l=P(e,0);l.setFullYear(a,0,i),l.setHours(0,0,0,0);const m=S(l,t);return n.getTime()>=u.getTime()?a+1:n.getTime()>=m.getTime()?a:a-1}function ze(e,t){var u,l,m,f;const n=E(),a=(t==null?void 0:t.firstWeekContainsDate)??((l=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:l.firstWeekContainsDate)??n.firstWeekContainsDate??((f=(m=n.locale)==null?void 0:m.options)==null?void 0:f.firstWeekContainsDate)??1,r=$(e,t),i=P(e,0);return i.setFullYear(r,0,a),i.setHours(0,0,0,0),S(i,t)}function Ke(e,t){const n=x(e),a=+S(n,t)-+ze(n,t);return Math.round(a/G)+1}function c(e,t){const n=e<0?"-":"",a=Math.abs(e).toString().padStart(t,"0");return n+a}const p={y(e,t){const n=e.getFullYear(),a=n>0?n:1-n;return c(t==="yy"?a%100:a,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):c(n+1,2)},d(e,t){return c(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return c(e.getHours()%12||12,t.length)},H(e,t){return c(e.getHours(),t.length)},m(e,t){return c(e.getMinutes(),t.length)},s(e,t){return c(e.getSeconds(),t.length)},S(e,t){const n=t.length,a=e.getMilliseconds(),r=Math.trunc(a*Math.pow(10,n-3));return c(r,t.length)}},N={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Q={G:function(e,t,n){const a=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const a=e.getFullYear(),r=a>0?a:1-a;return n.ordinalNumber(r,{unit:"year"})}return p.y(e,t)},Y:function(e,t,n,a){const r=$(e,a),i=r>0?r:1-r;if(t==="YY"){const d=i%100;return c(d,2)}return t==="Yo"?n.ordinalNumber(i,{unit:"year"}):c(i,t.length)},R:function(e,t){const n=V(e);return c(n,t.length)},u:function(e,t){const n=e.getFullYear();return c(n,t.length)},Q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return c(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return c(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){const a=e.getMonth();switch(t){case"M":case"MM":return p.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){const a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return c(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){const r=Ke(e,a);return t==="wo"?n.ordinalNumber(r,{unit:"week"}):c(r,t.length)},I:function(e,t,n){const a=Ue(e);return t==="Io"?n.ordinalNumber(a,{unit:"week"}):c(a,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):p.d(e,t)},D:function(e,t,n){const a=Je(e);return t==="Do"?n.ordinalNumber(a,{unit:"dayOfYear"}):c(a,t.length)},E:function(e,t,n){const a=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){const r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return c(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});case"eeee":default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){const r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return c(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});case"cccc":default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){const a=e.getDay(),r=a===0?7:a;switch(t){case"i":return String(r);case"ii":return c(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const a=e.getHours();let r;switch(a===12?r=N.noon:a===0?r=N.midnight:r=a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){const a=e.getHours();let r;switch(a>=17?r=N.evening:a>=12?r=N.afternoon:a>=4?r=N.morning:r=N.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let a=e.getHours()%12;return a===0&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return p.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):p.H(e,t)},K:function(e,t,n){const a=e.getHours()%12;return t==="Ko"?n.ordinalNumber(a,{unit:"hour"}):c(a,t.length)},k:function(e,t,n){let a=e.getHours();return a===0&&(a=24),t==="ko"?n.ordinalNumber(a,{unit:"hour"}):c(a,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):p.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):p.s(e,t)},S:function(e,t){return p.S(e,t)},X:function(e,t,n){const a=e.getTimezoneOffset();if(a===0)return"Z";switch(t){case"X":return _(a);case"XXXX":case"XX":return M(a);case"XXXXX":case"XXX":default:return M(a,":")}},x:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"x":return _(a);case"xxxx":case"xx":return M(a);case"xxxxx":case"xxx":default:return M(a,":")}},O:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+X(a,":");case"OOOO":default:return"GMT"+M(a,":")}},z:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+X(a,":");case"zzzz":default:return"GMT"+M(a,":")}},t:function(e,t,n){const a=Math.trunc(e.getTime()/1e3);return c(a,t.length)},T:function(e,t,n){const a=e.getTime();return c(a,t.length)}};function X(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),i=a%60;return i===0?n+String(r):n+String(r)+t+c(i,2)}function _(e,t){return e%60===0?(e>0?"-":"+")+c(Math.abs(e)/60,2):M(e,t)}function M(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=c(Math.trunc(a/60),2),i=c(a%60,2);return n+r+t+i}const B=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},I=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Ze=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],a=n[1],r=n[2];if(!r)return B(e,t);let i;switch(a){case"P":i=t.dateTime({width:"short"});break;case"PP":i=t.dateTime({width:"medium"});break;case"PPP":i=t.dateTime({width:"long"});break;case"PPPP":default:i=t.dateTime({width:"full"});break}return i.replace("{{date}}",B(a,t)).replace("{{time}}",I(r,t))},et={p:I,P:Ze},tt=/^D+$/,nt=/^Y+$/,at=["D","DD","YY","YYYY"];function rt(e){return tt.test(e)}function st(e){return nt.test(e)}function it(e,t,n){const a=ot(e,t,n);if(console.warn(a),at.includes(e))throw new RangeError(a)}function ot(e,t,n){const a=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const ct=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,dt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ut=/^'([^]*?)'?$/,lt=/''/g,mt=/[a-zA-Z]/;function ht(e,t,n){var f,y,b,v;const a=E(),r=a.locale??Ie,i=a.firstWeekContainsDate??((y=(f=a.locale)==null?void 0:f.options)==null?void 0:y.firstWeekContainsDate)??1,d=a.weekStartsOn??((v=(b=a.locale)==null?void 0:b.options)==null?void 0:v.weekStartsOn)??0,u=x(e);if(!fe(u))throw new RangeError("Invalid time value");let l=t.match(dt).map(g=>{const h=g[0];if(h==="p"||h==="P"){const k=et[h];return k(g,r.formatLong)}return g}).join("").match(ct).map(g=>{if(g==="''")return{isToken:!1,value:"'"};const h=g[0];if(h==="'")return{isToken:!1,value:ft(g)};if(Q[h])return{isToken:!0,value:g};if(h.match(mt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+h+"`");return{isToken:!1,value:g}});r.localize.preprocessor&&(l=r.localize.preprocessor(u,l));const m={firstWeekContainsDate:i,weekStartsOn:d,locale:r};return l.map(g=>{if(!g.isToken)return g.value;const h=g.value;(st(h)||rt(h))&&it(h,t,String(e));const k=Q[h[0]];return k(u,h,r.localize,m)}).join("")}function ft(e){const t=e.match(ut);return t?t[1].replace(lt,"'"):e}const Pt=()=>{J();const[e,t]=j.useState([]),[n,a]=j.useState(!0),[r,i]=j.useState(null),[d,u]=j.useState(!1),[l,m]=j.useState(!1);j.useEffect(()=>{f()},[]);const f=async()=>{try{a(!0);const o=await Z();t(o)}catch(o){console.error("Error loading contact messages:",o),O({title:"Error",description:"Failed to load contact messages. Please try again.",variant:"destructive"})}finally{a(!1)}},y=async o=>{try{m(!0),await ee(o),t(e.map(w=>w.id===o?{...w,isRead:!0,readAt:new Date}:w)),r&&r.id===o&&i({...r,isRead:!0,readAt:new Date}),O({title:"Success",description:"Message marked as read.",variant:"default"})}catch(w){console.error("Error marking message as read:",w),O({title:"Error",description:"Failed to mark message as read. Please try again.",variant:"destructive"})}finally{m(!1)}},b=async o=>{try{m(!0),await te(o),t(e.map(w=>w.id===o?{...w,isRead:!1,readAt:null}:w)),r&&r.id===o&&i({...r,isRead:!1,readAt:null}),O({title:"Success",description:"Message marked as unread.",variant:"default"})}catch(w){console.error("Error marking message as unread:",w),O({title:"Error",description:"Failed to mark message as unread. Please try again.",variant:"destructive"})}finally{m(!1)}},v=o=>{i(o),u(!0),!o.isRead&&o.id&&y(o.id)},g=()=>{u(!1),i(null)},h=o=>{if(!o)return"N/A";const w=o.toDate?o.toDate():new Date(o);return ht(w,"MMM d, yyyy h:mm a")},k=e.filter(o=>!o.isRead).length;return s.jsxs(oe,{title:"Contact Messages",description:"View and manage contact messages from users",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Contact Messages"}),s.jsxs("p",{className:"text-gray-600",children:["View and respond to messages from users",k>0&&s.jsxs(A,{variant:"destructive",className:"ml-2",children:[k," unread"]})]})]}),s.jsx(T,{onClick:f,variant:"outline",className:"mt-4 md:mt-0",disabled:n,children:n?s.jsxs(s.Fragment,{children:[s.jsx(C,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(U,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),n?s.jsxs("div",{className:"flex justify-center items-center py-12",children:[s.jsx(C,{className:"h-8 w-8 animate-spin text-burgundy-500"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Loading messages..."})]}):e.length===0?s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[s.jsx(z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Messages Yet"}),s.jsx("p",{className:"text-gray-600",children:"There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page."})]}):s.jsx("div",{className:"space-y-4",children:e.map(o=>s.jsx("div",{className:`bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer ${o.isRead?"":"border-l-4 border-burgundy-500"}`,onClick:()=>v(o),children:s.jsxs("div",{className:"flex flex-col md:flex-row justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(K,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:"text-navy-800 font-medium",children:o.email}),!o.isRead&&s.jsx(A,{variant:"default",className:"ml-2",children:"New"})]}),s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(ce,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:"text-gray-600",children:o.phone})]}),s.jsx("p",{className:"text-gray-700 line-clamp-2 mb-2",children:o.message}),s.jsxs("div",{className:"text-xs text-gray-500",children:["Received: ",h(o.createdAt)]})]}),s.jsx("div",{className:"mt-4 md:mt-0 md:ml-4 flex items-center",children:s.jsx(T,{variant:"ghost",size:"sm",onClick:w=>{w.stopPropagation(),o.id&&(o.isRead?b(o.id):y(o.id))},disabled:l,children:o.isRead?s.jsxs(s.Fragment,{children:[s.jsx(F,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):s.jsxs(s.Fragment,{children:[s.jsx(R,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})})]})},o.id))}),s.jsx(ne,{open:d,onOpenChange:g,children:s.jsxs(ae,{className:"sm:max-w-lg",children:[s.jsxs(re,{children:[s.jsx(se,{children:"Message Details"}),s.jsx(ie,{children:"Contact message from user"})]}),r&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"From:"}),s.jsx("div",{className:"col-span-2",children:r.email})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Phone:"}),s.jsx("div",{className:"col-span-2",children:r.phone})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Received:"}),s.jsx("div",{className:"col-span-2",children:h(r.createdAt)})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Status:"}),s.jsx("div",{className:"col-span-2",children:r.isRead?s.jsxs("span",{className:"text-green-600 flex items-center",children:[s.jsx(R,{className:"h-4 w-4 mr-1"}),"Read ",r.readAt&&`(${h(r.readAt)})`]}):s.jsxs("span",{className:"text-burgundy-600 flex items-center",children:[s.jsx(F,{className:"h-4 w-4 mr-1"}),"Unread"]})})]}),s.jsxs("div",{className:"py-2",children:[s.jsx("div",{className:"font-medium mb-2",children:"Message:"}),s.jsx("div",{className:"bg-gray-50 p-4 rounded-md whitespace-pre-wrap",children:r.message})]}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(T,{variant:"outline",onClick:g,children:"Close"}),s.jsx(T,{variant:r.isRead?"outline":"default",onClick:()=>{r.id&&(r.isRead?b(r.id):y(r.id))},disabled:l,children:l?s.jsx(C,{className:"h-4 w-4 animate-spin"}):r.isRead?s.jsxs(s.Fragment,{children:[s.jsx(F,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):s.jsxs(s.Fragment,{children:[s.jsx(R,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})]})]})]})})]})};export{Pt as default};
