import React from 'react';
import MainLayout from '@/components/layouts/MainLayout';
import { Mail } from 'lucide-react';
import { Button } from '@/components/ui/button-variants';

const DataDeletion = () => {
  // Function to open email client with pre-filled email
  const handleEmailClick = () => {
    window.location.href = 'mailto:<EMAIL>?subject=Delete My PeerBooks App Data';
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <div className="bg-white rounded-lg shadow-md p-8">
          <h1 className="text-3xl font-bold text-navy-800 font-playfair mb-6">User Data Deletion Instructions</h1>
          
          <div className="prose prose-burgundy max-w-none">
            <p className="mb-6">
              If you wish to delete your account and all associated personal data collected via Facebook and Google Login, please follow these steps:
            </p>

            <div className="bg-beige-100 p-6 rounded-lg border border-beige-300 mb-8">
              <h2 className="text-xl font-bold text-navy-800 mb-4">Deletion Request Process</h2>
              
              <ol className="list-decimal pl-6 space-y-4">
                <li>
                  <strong>Send an email to</strong>: <a href="mailto:<EMAIL>?subject=Delete My PeerBooks App Data" className="text-burgundy-500 hover:underline"><EMAIL></a> with the subject line: <span className="font-medium">Delete My PeerBooks App Data</span>.
                </li>
                <li>
                  <strong>Include the following information</strong>:
                  <ul className="list-disc pl-6 mt-2">
                    <li>Your full name as it appears in your PeerBooks account</li>
                    <li>The email address associated with your PeerBooks account</li>
                    <li>If you used social login, specify whether you used Google or Facebook authentication</li>
                  </ul>
                </li>
                <li>
                  <strong>Confirmation</strong>: Your data will be deleted within 48 hours, and you will receive a confirmation email once the deletion is complete.
                </li>
              </ol>

              <div className="mt-6">
                <Button 
                  onClick={handleEmailClick}
                  className="flex items-center gap-2"
                >
                  <Mail className="h-4 w-4" />
                  Send Deletion Request Email
                </Button>
              </div>
            </div>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">What Data Will Be Deleted</h2>
            <p>
              When you request account deletion, we will remove:
            </p>
            <ul className="list-disc pl-6 mb-6">
              <li>Your personal profile information (name, email, phone number, address)</li>
              <li>Your book listings and associated images</li>
              <li>Your transaction history</li>
              <li>Any reviews or comments you've posted</li>
              <li>All authentication data including social login connections</li>
            </ul>

            <h2 className="text-xl font-bold text-navy-800 mt-8 mb-4">Social Login Data</h2>
            <p>
              We respect your privacy and follow Facebook's and Google's Platform Policies to ensure your data is handled responsibly. When you delete your account:
            </p>
            <ul className="list-disc pl-6 mb-6">
              <li>All data obtained through Facebook Login or Google Sign-In will be permanently deleted from our systems</li>
              <li>Any connections between your PeerBooks account and your social media accounts will be severed</li>
            </ul>
            
            <div className="bg-gray-100 p-6 rounded-lg border border-gray-300 mt-8">
              <h3 className="text-lg font-bold text-navy-800 mb-2">Additional Steps for Social Login Users</h3>
              <p className="mb-4">
                To completely revoke PeerBooks' access to your social media accounts, we recommend also removing the app permissions from your social media settings:
              </p>
              <ul className="list-disc pl-6">
                <li>
                  <strong>For Facebook</strong>: Go to Facebook Settings → Apps and Websites → Find PeerBooks and remove it
                </li>
                <li>
                  <strong>For Google</strong>: Go to your Google Account → Security → Third-party apps with account access → Find PeerBooks and remove access
                </li>
              </ul>
            </div>
            
            <p className="mt-8">
              If you have any questions about the data deletion process, please contact us at <a href="mailto:<EMAIL>" className="text-burgundy-500 hover:underline"><EMAIL></a>.
            </p>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DataDeletion;
