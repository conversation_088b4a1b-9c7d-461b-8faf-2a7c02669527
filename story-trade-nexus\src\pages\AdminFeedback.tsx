/**
 * Admin Feedback Management Page
 * 
 * Allows administrators to view and manage feedback submissions
 */

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { 
  MessageSquare, 
  RefreshCw, 
  Eye, 
  EyeOff, 
  Loader2, 
  Star,
  TrendingUp,
  Users,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/lib/AuthContext';
import { 
  FeedbackSubmission, 
  FeedbackStats,
  getAllFeedback, 
  markFeedbackAsRead, 
  getFeedbackStats 
} from '@/lib/feedbackService';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import AdminLayout from '@/components/layout/AdminLayout';
import StarRating from '@/components/StarRating';

const AdminFeedback: React.FC = () => {
  const { currentUser } = useAuth();
  const [feedback, setFeedback] = useState<FeedbackSubmission[]>([]);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackSubmission | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Load feedback and stats
  const loadFeedback = async () => {
    try {
      setLoading(true);
      const [feedbackData, statsData] = await Promise.all([
        getAllFeedback(),
        getFeedbackStats()
      ]);
      setFeedback(feedbackData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading feedback:', error);
      toast({
        title: 'Error',
        description: 'Failed to load feedback data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadFeedback();
  }, []);

  // Open feedback dialog
  const openFeedbackDialog = async (feedbackItem: FeedbackSubmission) => {
    setSelectedFeedback(feedbackItem);
    setDialogOpen(true);

    // Mark as read if not already read
    if (!feedbackItem.isRead && feedbackItem.id) {
      try {
        await markFeedbackAsRead(feedbackItem.id);
        
        // Update local state
        setFeedback(prev => 
          prev.map(item => 
            item.id === feedbackItem.id 
              ? { ...item, isRead: true, readAt: new Date() }
              : item
          )
        );

        // Update stats
        if (stats) {
          setStats(prev => prev ? { ...prev, unreadCount: prev.unreadCount - 1 } : null);
        }
      } catch (error) {
        console.error('Error marking feedback as read:', error);
      }
    }
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors = {
      'Bug Report': 'bg-red-100 text-red-800',
      'Feature Request': 'bg-blue-100 text-blue-800',
      'General Feedback': 'bg-green-100 text-green-800',
      'Technical Support': 'bg-yellow-100 text-yellow-800',
      'Account Issues': 'bg-purple-100 text-purple-800',
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const unreadCount = feedback.filter(item => !item.isRead).length;

  return (
    <AdminLayout title="Feedback Management" description="View and manage user feedback submissions">
      {/* Header with Stats */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-navy-800 mb-2">Feedback Management</h1>
          <p className="text-gray-600">
            View and respond to user feedback and support requests
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount} unread
              </Badge>
            )}
          </p>
        </div>
        
        <Button 
          onClick={loadFeedback} 
          variant="outline" 
          className="mt-4 md:mt-0"
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubmissions}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unread</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.unreadCount}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating || 'N/A'}</div>
              {stats.averageRating > 0 && (
                <StarRating value={stats.averageRating} readonly size="sm" showText={false} />
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Most Common</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-bold">
                {Object.entries(stats.categoryBreakdown).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Feedback List */}
      <div className="space-y-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-4 animate-pulse">
              <div className="flex justify-between items-start mb-2">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-6 bg-gray-200 rounded w-20"></div>
              </div>
              <div className="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))
        ) : feedback.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No feedback yet</h3>
            <p className="text-gray-500">Feedback submissions will appear here when users submit them.</p>
          </div>
        ) : (
          feedback.map((item) => (
            <div
              key={item.id}
              className={`bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer ${
                !item.isRead ? 'border-l-4 border-burgundy-500' : ''
              }`}
              onClick={() => openFeedbackDialog(item)}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-2">
                <div className="flex items-center gap-2 mb-2 md:mb-0">
                  <h3 className="font-medium text-navy-800">{item.subject}</h3>
                  {!item.isRead && (
                    <Badge variant="default">New</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getCategoryColor(item.category)}>
                    {item.category}
                  </Badge>
                  {item.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm text-gray-600">{item.rating}</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                <span className="font-medium">{item.name}</span>
                <span>{item.email}</span>
                <span>{format(new Date(item.createdAt.seconds * 1000), 'MMM dd, yyyy HH:mm')}</span>
              </div>
              
              <p className="text-gray-700 line-clamp-2">
                {item.message}
              </p>
            </div>
          ))
        )}
      </div>

      {/* Feedback Detail Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              {selectedFeedback?.subject}
            </DialogTitle>
            <DialogDescription>
              Feedback submitted by {selectedFeedback?.name} on{' '}
              {selectedFeedback && format(new Date(selectedFeedback.createdAt.seconds * 1000), 'MMMM dd, yyyy at HH:mm')}
            </DialogDescription>
          </DialogHeader>
          
          {selectedFeedback && (
            <div className="space-y-4">
              {/* Feedback Details */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Name:</span>
                  <p>{selectedFeedback.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Email:</span>
                  <p>{selectedFeedback.email}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Category:</span>
                  <Badge className={getCategoryColor(selectedFeedback.category)}>
                    {selectedFeedback.category}
                  </Badge>
                </div>
                {selectedFeedback.rating && (
                  <div>
                    <span className="font-medium text-gray-700">Rating:</span>
                    <StarRating value={selectedFeedback.rating} readonly size="sm" />
                  </div>
                )}
              </div>

              {/* Message */}
              <div>
                <span className="font-medium text-gray-700">Message:</span>
                <div className="mt-2 p-3 bg-gray-50 rounded-md">
                  <p className="whitespace-pre-wrap">{selectedFeedback.message}</p>
                </div>
              </div>

              {/* Technical Details */}
              {selectedFeedback.userAgent && (
                <div>
                  <span className="font-medium text-gray-700">User Agent:</span>
                  <p className="text-xs text-gray-500 mt-1">{selectedFeedback.userAgent}</p>
                </div>
              )}

              {/* Status */}
              <div className="flex items-center gap-2 pt-4 border-t">
                {selectedFeedback.isRead ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <Eye className="h-4 w-4" />
                    <span className="text-sm">
                      Read {selectedFeedback.readAt && format(new Date(selectedFeedback.readAt.seconds * 1000), 'MMM dd, yyyy HH:mm')}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-gray-500">
                    <EyeOff className="h-4 w-4" />
                    <span className="text-sm">Unread</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminFeedback;
