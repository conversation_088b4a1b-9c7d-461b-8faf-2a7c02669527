import{j as e,M as o,a as l,s as a}from"./index-BHNYm6xc.js";const n=()=>{const s=()=>{window.location.href="mailto:<EMAIL>?subject=Delete My PeerBooks App Data"};return e.jsx(o,{children:e.jsx("div",{className:"container mx-auto px-4 py-12 max-w-4xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-navy-800 font-playfair mb-6",children:"User Data Deletion Instructions"}),e.jsxs("div",{className:"prose prose-burgundy max-w-none",children:[e.jsx("p",{className:"mb-6",children:"If you wish to delete your account and all associated personal data collected via Facebook and Google Login, please follow these steps:"}),e.jsxs("div",{className:"bg-beige-100 p-6 rounded-lg border border-beige-300 mb-8",children:[e.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-4",children:"Deletion Request Process"}),e.jsxs("ol",{className:"list-decimal pl-6 space-y-4",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Send an email to"}),": ",e.jsx("a",{href:"mailto:<EMAIL>?subject=Delete My PeerBooks App Data",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})," with the subject line: ",e.jsx("span",{className:"font-medium",children:"Delete My PeerBooks App Data"}),"."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Include the following information"}),":",e.jsxs("ul",{className:"list-disc pl-6 mt-2",children:[e.jsx("li",{children:"Your full name as it appears in your PeerBooks account"}),e.jsx("li",{children:"The email address associated with your PeerBooks account"}),e.jsx("li",{children:"If you used social login, specify whether you used Google or Facebook authentication"})]})]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Confirmation"}),": Your data will be deleted within 48 hours, and you will receive a confirmation email once the deletion is complete."]})]}),e.jsx("div",{className:"mt-6",children:e.jsxs(l,{onClick:s,className:"flex items-center gap-2",children:[e.jsx(a,{className:"h-4 w-4"}),"Send Deletion Request Email"]})})]}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"What Data Will Be Deleted"}),e.jsx("p",{children:"When you request account deletion, we will remove:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-6",children:[e.jsx("li",{children:"Your personal profile information (name, email, phone number, address)"}),e.jsx("li",{children:"Your book listings and associated images"}),e.jsx("li",{children:"Your transaction history"}),e.jsx("li",{children:"Any reviews or comments you've posted"}),e.jsx("li",{children:"All authentication data including social login connections"})]}),e.jsx("h2",{className:"text-xl font-bold text-navy-800 mt-8 mb-4",children:"Social Login Data"}),e.jsx("p",{children:"We respect your privacy and follow Facebook's and Google's Platform Policies to ensure your data is handled responsibly. When you delete your account:"}),e.jsxs("ul",{className:"list-disc pl-6 mb-6",children:[e.jsx("li",{children:"All data obtained through Facebook Login or Google Sign-In will be permanently deleted from our systems"}),e.jsx("li",{children:"Any connections between your PeerBooks account and your social media accounts will be severed"})]}),e.jsxs("div",{className:"bg-gray-100 p-6 rounded-lg border border-gray-300 mt-8",children:[e.jsx("h3",{className:"text-lg font-bold text-navy-800 mb-2",children:"Additional Steps for Social Login Users"}),e.jsx("p",{className:"mb-4",children:"To completely revoke PeerBooks' access to your social media accounts, we recommend also removing the app permissions from your social media settings:"}),e.jsxs("ul",{className:"list-disc pl-6",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"For Facebook"}),": Go to Facebook Settings → Apps and Websites → Find PeerBooks and remove it"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"For Google"}),": Go to your Google Account → Security → Third-party apps with account access → Find PeerBooks and remove access"]})]})]}),e.jsxs("p",{className:"mt-8",children:["If you have any questions about the data deletion process, please contact us at ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"}),"."]})]})]})})})};export{n as default};
