/**
 * Tests for Book Sorting Utilities
 */

import { sortBooks, SortCriteria } from '../bookSortingUtils';
import { Book } from '@/types/index';

// Mock book data for testing
const mockBooks: Book[] = [
  {
    id: '1',
    title: 'Book A',
    author: 'Author A',
    genre: ['Fiction'],
    condition: 'Good',
    description: 'Test book A',
    imageUrl: 'test.jpg',
    perceivedValue: 8,
    price: 100,
    rentalPrice: 10,
    rentalPeriod: 'per day',
    availability: 'For Rent, Sale & Exchange',
    ownerId: 'user1',
    ownerName: 'User 1',
    ownerCommunity: 'Community A',
    ownerRating: 4.5,
    distance: 2.5,
    createdAt: new Date('2023-01-01'),
  },
  {
    id: '2',
    title: 'Book B',
    author: 'Author B',
    genre: ['Fiction'],
    condition: 'New',
    description: 'Test book B',
    imageUrl: 'test.jpg',
    perceivedValue: 9,
    price: 200,
    rentalPrice: 20,
    rentalPeriod: 'per day',
    availability: 'For Rent, Sale & Exchange',
    ownerId: 'user2',
    ownerName: 'User 2',
    ownerCommunity: 'Community B',
    ownerRating: 4.8,
    distance: 1.0,
    createdAt: new Date('2023-02-01'),
  },
  {
    id: '3',
    title: 'Book C',
    author: 'Author C',
    genre: ['Fiction'],
    condition: 'Fair',
    description: 'Test book C',
    imageUrl: 'test.jpg',
    perceivedValue: 6,
    price: 50,
    availability: 'For Sale',
    ownerId: 'user3',
    ownerName: 'User 3',
    ownerCommunity: 'Community A',
    ownerRating: 4.2,
    distance: 5.0,
    createdAt: new Date('2023-03-01'),
  }
];

describe('Book Sorting Utils', () => {
  describe('sortBooks', () => {
    it('should sort by community first, then distance', () => {
      const sorted = sortBooks(mockBooks, 'community-distance', 'Community A');
      
      // Books from Community A should come first
      expect(sorted[0].ownerCommunity).toBe('Community A');
      expect(sorted[1].ownerCommunity).toBe('Community A');
      expect(sorted[2].ownerCommunity).toBe('Community B');
      
      // Within Community A, closer book should come first
      expect(sorted[0].distance).toBeLessThan(sorted[1].distance!);
    });

    it('should sort by price low to high', () => {
      const sorted = sortBooks(mockBooks, 'price-low-high', 'Community A');
      
      // Community A books should still come first
      expect(sorted[0].ownerCommunity).toBe('Community A');
      expect(sorted[1].ownerCommunity).toBe('Community A');
      
      // Within Community A, lower price should come first
      expect(sorted[0].price).toBeLessThan(sorted[1].price!);
    });

    it('should sort by price high to low', () => {
      const sorted = sortBooks(mockBooks, 'price-high-low', 'Community A');
      
      // Community A books should still come first
      expect(sorted[0].ownerCommunity).toBe('Community A');
      expect(sorted[1].ownerCommunity).toBe('Community A');
      
      // Within Community A, higher price should come first
      expect(sorted[0].price).toBeGreaterThan(sorted[1].price!);
    });

    it('should sort by rental price low to high', () => {
      const sorted = sortBooks(mockBooks, 'rental-low-high', 'Community A');
      
      // Only books with rental prices should be prioritized
      const booksWithRental = sorted.filter(book => book.rentalPrice);
      expect(booksWithRental.length).toBeGreaterThan(0);
    });

    it('should sort by newest first', () => {
      const sorted = sortBooks(mockBooks, 'newest-first', 'Community A');
      
      // Community A books should still come first
      expect(sorted[0].ownerCommunity).toBe('Community A');
      expect(sorted[1].ownerCommunity).toBe('Community A');
      
      // Within Community A, newer book should come first
      expect(sorted[0].createdAt.getTime()).toBeGreaterThan(sorted[1].createdAt.getTime());
    });

    it('should sort by distance only when criteria is distance', () => {
      const sorted = sortBooks(mockBooks, 'distance');
      
      // Should sort purely by distance, ignoring community
      expect(sorted[0].distance).toBe(1.0); // Book B
      expect(sorted[1].distance).toBe(2.5); // Book A
      expect(sorted[2].distance).toBe(5.0); // Book C
    });

    it('should handle empty array', () => {
      const sorted = sortBooks([], 'community-distance');
      expect(sorted).toEqual([]);
    });

    it('should handle books without distance data', () => {
      const booksWithoutDistance = mockBooks.map(book => ({ ...book, distance: undefined }));
      const sorted = sortBooks(booksWithoutDistance, 'community-distance', 'Community A');
      
      // Should still prioritize community
      expect(sorted[0].ownerCommunity).toBe('Community A');
      expect(sorted[1].ownerCommunity).toBe('Community A');
    });
  });
});
