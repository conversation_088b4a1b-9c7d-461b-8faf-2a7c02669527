/**
 * Feedback & Support Page
 * 
 * Comprehensive feedback and support page for PeerBooks users
 */

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  MessageSquare, 
  Send, 
  Loader2, 
  HelpCircle, 
  Clock, 
  Mail, 
  Phone,
  AlertCircle,
  CheckCircle,
  Star
} from 'lucide-react';
import { useAuth } from '@/lib/AuthContext';
import { submitFeedback, checkRateLimit, formatRemainingTime } from '@/lib/feedbackService';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import StarRating from '@/components/StarRating';

// Define the form schema with validation
const feedbackSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }).max(100),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  subject: z.string().min(5, { message: 'Subject must be at least 5 characters' }).max(200),
  category: z.enum(['Bug Report', 'Feature Request', 'General Feedback', 'Technical Support', 'Account Issues'], {
    required_error: 'Please select a category'
  }),
  message: z.string()
    .min(10, { message: 'Message must be at least 10 characters' })
    .max(2000, { message: 'Message must be less than 2000 characters' }),
  rating: z.number().min(1).max(5).optional(),
});

// Define the form values type
type FeedbackValues = z.infer<typeof feedbackSchema>;

// FAQ data
const faqData = [
  {
    question: "How do I add books to the platform?",
    answer: "After creating an account and verifying your email, go to 'Add Your Books' in the navigation menu. Fill out the book details form with title, author, condition, and upload photos."
  },
  {
    question: "How does the book exchange process work?",
    answer: "Browse available books, contact the owner through WhatsApp or email, arrange the exchange details, and meet safely to exchange books. Always verify the book condition before finalizing."
  },
  {
    question: "Is my personal information safe?",
    answer: "Yes, we take privacy seriously. Your email is never displayed publicly, and we only share your contact information when you initiate contact with a book owner."
  },
  {
    question: "How do I report inappropriate content or users?",
    answer: "Use the 'Bug Report' or 'General Feedback' category in this form to report any issues. We review all reports promptly and take appropriate action."
  },
  {
    question: "Can I edit or delete my book listings?",
    answer: "Yes, go to your Dashboard to manage your book listings. You can edit details, update availability, or remove books from the platform."
  }
];

const Feedback: React.FC = () => {
  const { currentUser } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rating, setRating] = useState<number>(0);

  // Initialize the form with react-hook-form and zod validation
  const form = useForm<FeedbackValues>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      name: currentUser?.displayName || '',
      email: currentUser?.email || '',
      subject: '',
      category: undefined,
      message: '',
      rating: undefined,
    },
  });

  // Handle form submission
  const onSubmit = async (data: FeedbackValues) => {
    try {
      setIsSubmitting(true);

      // Check rate limit before submitting
      const rateLimitCheck = checkRateLimit();
      if (!rateLimitCheck.allowed) {
        const remainingTime = formatRemainingTime(rateLimitCheck.remainingTime || 0);
        toast({
          title: 'Rate Limit Exceeded',
          description: `Please wait ${remainingTime} before submitting another feedback.`,
          variant: 'destructive',
        });
        return;
      }

      // Submit the feedback
      await submitFeedback({
        name: data.name,
        email: data.email,
        subject: data.subject,
        category: data.category,
        message: data.message,
        rating: rating > 0 ? rating : undefined,
      });

      // Show success message
      toast({
        title: 'Feedback Sent Successfully',
        description: 'Thank you for your feedback! We will review it and get back to you within 24-48 hours.',
        variant: 'default',
      });

      // Reset the form
      form.reset({
        name: currentUser?.displayName || '',
        email: currentUser?.email || '',
        subject: '',
        category: undefined,
        message: '',
        rating: undefined,
      });
      setRating(0);

    } catch (error: any) {
      console.error('Error submitting feedback:', error);

      // Show error message
      toast({
        title: 'Error',
        description: error.message || 'There was an error sending your feedback. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />

      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-12">
          {/* Page Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-playfair font-bold text-navy-800 mb-4">
              Feedback & Support
            </h1>
            <p className="text-gray-600 max-w-3xl mx-auto">
              We value your feedback and are here to help! Whether you've found a bug, have a feature request, 
              or need technical support, we'd love to hear from you. Your input helps us make PeerBooks better for everyone.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* FAQ Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <div className="flex items-center mb-6">
                  <HelpCircle className="h-6 w-6 text-burgundy-500 mr-3" />
                  <h2 className="text-xl font-semibold text-navy-800">
                    Frequently Asked Questions
                  </h2>
                </div>

                <div className="space-y-4">
                  {faqData.map((faq, index) => (
                    <details key={index} className="group">
                      <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-burgundy-600 transition-colors">
                        {faq.question}
                      </summary>
                      <p className="mt-2 text-sm text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </details>
                  ))}
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-navy-800 mb-4">
                  Need Immediate Help?
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-burgundy-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Response Time</p>
                      <p className="text-sm text-gray-600">Within 24-48 hours</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Mail className="h-5 w-5 text-burgundy-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">Email Support</p>
                      <p className="text-sm text-gray-600">Available 24/7</p>
                    </div>
                  </div>
                </div>

                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    For urgent account issues or security concerns, please use the "Account Issues" category in the feedback form.
                  </AlertDescription>
                </Alert>
              </div>
            </div>

            {/* Feedback Form */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center mb-6">
                  <MessageSquare className="h-6 w-6 text-burgundy-500 mr-3" />
                  <h2 className="text-xl font-semibold text-navy-800">
                    Send Us Your Feedback
                  </h2>
                </div>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Name and Email Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Your full name" 
                                {...field}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email *</FormLabel>
                            <FormControl>
                              <Input 
                                type="email"
                                placeholder="<EMAIL>" 
                                {...field}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Subject and Category Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="subject"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Subject *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Brief description of your feedback" 
                                {...field}
                                disabled={isSubmitting}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category *</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                              disabled={isSubmitting}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="Bug Report">Bug Report</SelectItem>
                                <SelectItem value="Feature Request">Feature Request</SelectItem>
                                <SelectItem value="General Feedback">General Feedback</SelectItem>
                                <SelectItem value="Technical Support">Technical Support</SelectItem>
                                <SelectItem value="Account Issues">Account Issues</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Rating */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Overall Experience (Optional)
                      </label>
                      <StarRating
                        value={rating}
                        onChange={setRating}
                        disabled={isSubmitting}
                        showText={true}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Rate your overall experience with PeerBooks
                      </p>
                    </div>

                    {/* Message */}
                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Message *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Please provide detailed information about your feedback, including steps to reproduce any issues..."
                              className="min-h-[120px] resize-y"
                              {...field}
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-xs text-gray-500">
                            {field.value?.length || 0}/2000 characters
                          </p>
                        </FormItem>
                      )}
                    />

                    {/* Privacy Notice */}
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        <strong>Privacy Notice:</strong> Your feedback will be used solely to improve PeerBooks. 
                        We will not share your information with third parties and will respond to your email address provided above.
                      </AlertDescription>
                    </Alert>

                    {/* Submit Button */}
                    <Button 
                      type="submit" 
                      className="w-full"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending Feedback...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Send Feedback
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Feedback;
