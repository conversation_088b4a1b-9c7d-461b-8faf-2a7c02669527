import{f as V,j as e,U as Y,r as u,u as _,J as N,Q,H as J,S as q,I as K,ae as S,h as C,l as O,T as v,ac as W,aV as z,L as X,G as Z}from"./index-CrTHSN9_.js";import{S as ee,a as se,b as te,c as re,d as ie}from"./select-B-kGn_kw.js";import"./index-C-MsGQQt.js";import"./chevron-up-BAohs_HQ.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=V("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),A=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],y=s=>!s.availability.includes("Sale")||!s.price?null:s.price,w=s=>{var r;if(!s.availability.includes("Rent")||!s.rentalPrice)return null;const o=s.rentalPrice,n=((r=s.rentalPeriod)==null?void 0:r.toLowerCase())||"per day";return n.includes("week")?o/7:n.includes("month")?o/30:n.includes("year")?o/365:o},c=s=>(s.createdAt instanceof Date?s.createdAt:new Date(s.createdAt)).getTime(),le=(s,o,n)=>!s||s.length===0?[]:[...s].sort((r,t)=>{if(o!=="distance"){const l=n&&r.ownerCommunity&&r.ownerCommunity===n,a=n&&t.ownerCommunity&&t.ownerCommunity===n;if(l&&!a)return-1;if(a&&!l)return 1}switch(o){case"community-distance":return r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:r.distance!==void 0?-1:t.distance!==void 0?1:c(t)-c(r);case"price-low-high":{const l=y(r),a=y(t);return l!==null&&a!==null?l-a:l!==null?-1:a!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"price-high-low":{const l=y(r),a=y(t);return l!==null&&a!==null?a-l:l!==null?-1:a!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"rental-low-high":{const l=w(r),a=w(t);return l!==null&&a!==null?l-a:l!==null?-1:a!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"rental-high-low":{const l=w(r),a=w(t);return l!==null&&a!==null?a-l:l!==null?-1:a!==null?1:r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:c(t)-c(r)}case"distance":return r.distance!==void 0&&t.distance!==void 0?r.distance-t.distance:r.distance!==void 0?-1:t.distance!==void 0?1:c(t)-c(r);case"newest-first":return c(t)-c(r);case"oldest-first":return c(r)-c(t);default:return c(t)-c(r)}}),G=()=>"community-distance",ae=(s,o)=>{switch(o){case"price-low-high":case"price-high-low":return s.some(n=>y(n)!==null);case"rental-low-high":case"rental-high-low":return s.some(n=>w(n)!==null);case"distance":case"community-distance":return s.some(n=>n.distance!==void 0);default:return!0}},ne=s=>A.filter(o=>ae(s,o.value)),oe=({sortCriteria:s,onSortChange:o,books:n,disabled:r=!1,className:t=""})=>{const l=ne(n),a=A.find(d=>d.value===s),j=d=>{switch(d){case"price-low-high":case"price-high-low":return n.filter(m=>m.availability.includes("Sale")&&m.price).length;case"rental-low-high":case"rental-high-low":return n.filter(m=>m.availability.includes("Rent")&&m.rentalPrice).length;case"distance":case"community-distance":return n.filter(m=>m.distance!==void 0).length;default:return n.length}};return e.jsx("div",{className:t,children:e.jsxs(ee,{value:s,onValueChange:d=>o(d),disabled:r,children:[e.jsx(se,{className:"h-10 w-full",children:e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(M,{className:"h-4 w-4 text-gray-500 flex-shrink-0"}),e.jsx(te,{placeholder:"Sort by...",children:e.jsx("span",{className:"text-sm",children:(a==null?void 0:a.label)||"Sort by..."})})]})}),e.jsx(re,{children:l.map(d=>{const m=j(d.value),g=d.value===s;return e.jsx(ie,{value:d.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-start justify-between w-full",children:e.jsxs("div",{className:"flex flex-col flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:d.label}),g&&e.jsx(Y,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:d.description}),m<n.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[m," of ",n.length," books have this data"]})]})})},d.value)})})]})})},H="peerbooks-sort-preference",ce=()=>{const[s,o]=u.useState(G());u.useEffect(()=>{try{const l=localStorage.getItem(H);if(l){const a=l;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(a)&&o(a)}}catch(l){console.warn("Failed to load sort preference from localStorage:",l)}},[]);const n=u.useCallback(l=>{o(l);try{localStorage.setItem(H,l)}catch(a){console.warn("Failed to save sort preference to localStorage:",a)}},[]),r=u.useCallback((l,a)=>le(l,s,a),[s]),t=u.useCallback(()=>{const l=G();n(l)},[n]);return{sortCriteria:s,setSortCriteria:n,sortBooks:r,resetSort:t}},xe=()=>{var I;const{userData:s}=_(),{sortCriteria:o,setSortCriteria:n,sortBooks:r}=ce(),[t,l]=u.useState(""),[a,j]=u.useState("All"),[d,m]=u.useState("All"),[g,$]=u.useState([]),[h,F]=u.useState(!0),[L,k]=u.useState(null),[b,p]=u.useState(null),D=["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"],U=["All","For Rent","For Sale","For Exchange"];u.useEffect(()=>{R()},[]);const R=async()=>{try{F(!0),k(null),p("loading"),console.log("BrowseBooks: Fetching books from Firebase"),N.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const i=s==null?void 0:s.community;console.log("BrowseBooks: User community for sorting:",i);const x=await Q(!1,i);if(x.some(f=>f.distance!==void 0)){p("success");const f=i?`Books sorted by community (${i} first) and distance`:"Books sorted by distance (closest first)";N.success(f,{id:"location-toast",duration:4e3})}else{p("error");const f=i?`Books sorted by community (${i} first) and newest first`:"Books sorted by newest first";N.info(f,{id:"location-toast",duration:3e3})}$(x),console.log(`BrowseBooks: Fetched ${x.length} books from Firebase`)}catch(i){console.error("Error fetching books:",i),p("error"),i instanceof Error?(k(`Failed to load books: ${i.message}. Please try again.`),(i.message.includes("permission")||i.message.includes("denied"))&&(p("denied"),N.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):k("Failed to load books. Please try again.")}finally{F(!1)}},P=()=>{console.log("BrowseBooks: Refreshing books"),R()},E=g.filter(i=>{const x=t===""||i.title.toLowerCase().includes(t.toLowerCase())||i.author.toLowerCase().includes(t.toLowerCase()),T=a==="All"||i.genre.includes(a),f=d==="All"||i.availability.includes(d);return x&&T&&f}),B=r(E,s==null?void 0:s.community);return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(J,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsxs("div",{className:"hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2",children:[e.jsx("div",{className:"sm:col-span-2 lg:col-span-1",children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Search"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Genre"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Availability"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Sort By"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative sm:col-span-2 lg:col-span-1",children:[e.jsx(q,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(K,{type:"text",placeholder:"Search by title or author...",className:"pl-10 h-10",value:t,onChange:i=>l(i.target.value),disabled:h})]}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:a,onChange:i=>j(i.target.value),disabled:h,"aria-label":"Filter by genre",children:D.map(i=>e.jsx("option",{value:i,children:i},i))})}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:d,onChange:i=>m(i.target.value),disabled:h,"aria-label":"Filter by availability",children:U.map(i=>e.jsx("option",{value:i,children:i},i))})}),e.jsx("div",{children:e.jsx(oe,{sortCriteria:o,onSortChange:n,books:E,disabled:h})})]}),o!=="community-distance"&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Active sort:"}),e.jsx("span",{children:(I=A.find(i=>i.value===o))==null?void 0:I.label}),e.jsx("button",{onClick:()=>n("community-distance"),className:"ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline",children:"Reset to default"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsxs("span",{className:"font-medium",children:["Showing ",B.length," of ",g.length," books"]}),(t||a!=="All"||d!=="All")&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[b==="loading"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),b==="success"&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and distance`:"Books sorted by distance (closest first)"})]}),b==="error"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and newest first`:"Books sorted by newest first"})]}),b==="denied"&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(S,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(C,{variant:"outline",onClick:P,disabled:h,className:"text-sm",children:h?e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),L&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:L}),e.jsx(C,{variant:"link",onClick:P,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),h?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((i,x)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(v,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(v,{className:"h-6 w-3/4 mb-2"}),e.jsx(v,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(v,{className:"h-8 w-20"}),e.jsx(v,{className:"h-8 w-20"})]})]})]},x))}):B.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:B.map(i=>e.jsx(W,{book:i},i.id))}):g.length===0?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(z,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(X,{to:"/add-books",children:e.jsx(C,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(Z,{})]})};export{xe as default};
