import{ah as V,r as o,ai as z,at as K,j as e,aj as U,ak as q,al as X,aN as $,a0 as B,O as J,X as Y,aO as l,I as j,a as T,J as D,aP as Q}from"./index-pJ8lwbxh.js";import{T as W}from"./textarea-C2yvQnK6.js";import{S as P,a as N,b as R,c as E,d as F}from"./select-BvpqnwEA.js";import{u as Z}from"./index-wsYRTj5A.js";import{S as ee}from"./save-DV2CKmL-.js";import"./chevron-up-CiklY5wh.js";var I="Checkbox",[te,be]=V(I),[ie,ae]=te(I),O=o.forwardRef((a,v)=>{const{__scopeCheckbox:n,name:b,checked:i,defaultChecked:c,required:r,disabled:x,value:u="on",onCheckedChange:g,form:s,...C}=a,[m,t]=o.useState(null),h=z(v,p=>t(p)),d=o.useRef(!1),S=m?s||!!m.closest("form"):!0,[y=!1,A]=K({prop:i,defaultProp:c,onChange:g}),_=o.useRef(y);return o.useEffect(()=>{const p=m==null?void 0:m.form;if(p){const k=()=>A(_.current);return p.addEventListener("reset",k),()=>p.removeEventListener("reset",k)}},[m,A]),e.jsxs(ie,{scope:n,state:y,disabled:x,children:[e.jsx(U.button,{type:"button",role:"checkbox","aria-checked":f(y)?"mixed":y,"aria-required":r,"data-state":H(y),"data-disabled":x?"":void 0,disabled:x,value:u,...C,ref:h,onKeyDown:q(a.onKeyDown,p=>{p.key==="Enter"&&p.preventDefault()}),onClick:q(a.onClick,p=>{A(k=>f(k)?!0:!k),S&&(d.current=p.isPropagationStopped(),d.current||p.stopPropagation())})}),S&&e.jsx(re,{control:m,bubbles:!d.current,name:b,value:u,checked:y,required:r,disabled:x,form:s,style:{transform:"translateX(-100%)"},defaultChecked:f(c)?!1:c})]})});O.displayName=I;var G="CheckboxIndicator",L=o.forwardRef((a,v)=>{const{__scopeCheckbox:n,forceMount:b,...i}=a,c=ae(G,n);return e.jsx(X,{present:b||f(c.state)||c.state===!0,children:e.jsx(U.span,{"data-state":H(c.state),"data-disabled":c.disabled?"":void 0,...i,ref:v,style:{pointerEvents:"none",...a.style}})})});L.displayName=G;var re=a=>{const{control:v,checked:n,bubbles:b=!0,defaultChecked:i,...c}=a,r=o.useRef(null),x=Z(n),u=$(v);o.useEffect(()=>{const s=r.current,C=window.HTMLInputElement.prototype,t=Object.getOwnPropertyDescriptor(C,"checked").set;if(x!==n&&t){const h=new Event("click",{bubbles:b});s.indeterminate=f(n),t.call(s,f(n)?!1:n),s.dispatchEvent(h)}},[x,n,b]);const g=o.useRef(f(n)?!1:n);return e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??g.current,...c,tabIndex:-1,ref:r,style:{...a.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function f(a){return a==="indeterminate"}function H(a){return f(a)?"indeterminate":a?"checked":"unchecked"}var M=O,se=L;const w=o.forwardRef(({className:a,...v},n)=>e.jsx(M,{ref:n,className:B("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...v,children:e.jsx(se,{className:B("flex items-center justify-center text-current"),children:e.jsx(J,{className:"h-4 w-4"})})}));w.displayName=M.displayName;const ne=["New","Like New","Good","Fair"],le=["For Rent","For Exchange","For Sale","For Rent & Sale","For Rent & Exchange","For Sale & Exchange","For Rent, Sale & Exchange"],oe=["Available","Sold Out","Rented Out"],ce=["per day","per week","per month"],de=["Fiction","Non-Fiction","Mystery","Romance","Science Fiction","Fantasy","Biography","History","Self-Help","Business","Technology","Health","Travel","Cooking","Art","Religion","Philosophy","Poetry","Drama","Children","Young Adult","Educational","Reference"],fe=({book:a,isOpen:v,onClose:n,onBookUpdated:b})=>{const[i,c]=o.useState({title:"",author:"",isbn:"",genre:[],condition:"Good",description:"",availability:"For Exchange",price:void 0,rentalPrice:void 0,rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:void 0,imageUrls:[],status:"Available",nextAvailableDate:void 0}),[r,x]=o.useState(!1),[u,g]=o.useState([]);o.useEffect(()=>{a&&(c({title:a.title,author:a.author,isbn:a.isbn||"",genre:a.genre,condition:a.condition,description:a.description,availability:a.availability,price:a.price,rentalPrice:a.rentalPrice,rentalPeriod:a.rentalPeriod||"per week",securityDepositRequired:a.securityDepositRequired||!1,securityDepositAmount:a.securityDepositAmount,imageUrls:a.imageUrls||[a.imageUrl],status:a.status||"Available",nextAvailableDate:a.nextAvailableDate}),g(a.genre))},[a]);const s=(t,h)=>{c(d=>({...d,[t]:h}))},C=t=>{const h=u.includes(t)?u.filter(d=>d!==t):[...u,t];g(h),s("genre",h)},m=async t=>{var h;if(t.preventDefault(),!i.title.trim()||!i.author.trim()){D.error("Title and author are required");return}if(u.length===0){D.error("Please select at least one genre");return}x(!0);try{const d={title:i.title.trim(),author:i.author.trim(),isbn:((h=i.isbn)==null?void 0:h.trim())||void 0,genre:u,condition:i.condition,description:i.description.trim(),availability:i.availability,price:i.price,rentalPrice:i.rentalPrice,rentalPeriod:i.rentalPeriod,securityDepositRequired:i.securityDepositRequired,securityDepositAmount:i.securityDepositAmount,imageUrls:i.imageUrls,status:i.status,nextAvailableDate:i.nextAvailableDate};i.imageUrls&&i.imageUrls.length>0&&(d.imageUrl=i.imageUrls[0]),await Q(a.id,d);const S={...a,...d};b(S),D.success("Book updated successfully"),n()}catch(d){console.error("Error updating book:",d),D.error("Failed to update book")}finally{x(!1)}};return v?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsx("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:"Edit Book"}),e.jsx("button",{onClick:n,className:"text-gray-400 hover:text-gray-500",disabled:r,children:e.jsx(Y,{className:"h-6 w-6"})})]}),e.jsxs("form",{onSubmit:m,className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"title",children:"Book Title *"}),e.jsx(j,{id:"title",value:i.title,onChange:t=>s("title",t.target.value),placeholder:"Enter book title",disabled:r,required:!0})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"author",children:"Author *"}),e.jsx(j,{id:"author",value:i.author,onChange:t=>s("author",t.target.value),placeholder:"Enter author name",disabled:r,required:!0})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"isbn",children:"ISBN (Optional)"}),e.jsx(j,{id:"isbn",value:i.isbn,onChange:t=>s("isbn",t.target.value),placeholder:"Enter ISBN",disabled:r})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"condition",children:"Condition *"}),e.jsxs(P,{value:i.condition,onValueChange:t=>s("condition",t),disabled:r,children:[e.jsx(N,{children:e.jsx(R,{placeholder:"Select condition"})}),e.jsx(E,{children:ne.map(t=>e.jsx(F,{value:t,children:t},t))})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"status",children:"Current Status"}),e.jsxs(P,{value:i.status,onValueChange:t=>s("status",t),disabled:r,children:[e.jsx(N,{children:e.jsx(R,{placeholder:"Select status"})}),e.jsx(E,{children:oe.map(t=>e.jsx(F,{value:t,children:t},t))})]})]}),i.status==="Rented Out"&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"nextAvailableDate",children:"Expected Return Date"}),e.jsx(j,{id:"nextAvailableDate",type:"date",value:i.nextAvailableDate?i.nextAvailableDate.toISOString().split("T")[0]:"",onChange:t=>s("nextAvailableDate",t.target.value?new Date(t.target.value):void 0),disabled:r})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"availability",children:"Availability Type *"}),e.jsxs(P,{value:i.availability,onValueChange:t=>s("availability",t),disabled:r,children:[e.jsx(N,{children:e.jsx(R,{placeholder:"Select availability"})}),e.jsx(E,{children:le.map(t=>e.jsx(F,{value:t,children:t},t))})]})]}),i.availability.includes("Sale")&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"price",children:"Sale Price (₹)"}),e.jsx(j,{id:"price",type:"number",value:i.price||"",onChange:t=>s("price",t.target.value?Number(t.target.value):void 0),placeholder:"Enter sale price",disabled:r})]}),i.availability.includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(l,{htmlFor:"rentalPrice",children:"Rental Price (₹)"}),e.jsx(j,{id:"rentalPrice",type:"number",value:i.rentalPrice||"",onChange:t=>s("rentalPrice",t.target.value?Number(t.target.value):void 0),placeholder:"Enter rental price",disabled:r})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"rentalPeriod",children:"Rental Period"}),e.jsxs(P,{value:i.rentalPeriod,onValueChange:t=>s("rentalPeriod",t),disabled:r,children:[e.jsx(N,{children:e.jsx(R,{placeholder:"Select period"})}),e.jsx(E,{children:ce.map(t=>e.jsx(F,{value:t,children:t},t))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(w,{id:"securityDeposit",checked:i.securityDepositRequired,onCheckedChange:t=>s("securityDepositRequired",t),disabled:r}),e.jsx(l,{htmlFor:"securityDeposit",children:"Require Security Deposit"})]}),i.securityDepositRequired&&e.jsxs("div",{children:[e.jsx(l,{htmlFor:"securityDepositAmount",children:"Security Deposit Amount (₹)"}),e.jsx(j,{id:"securityDepositAmount",type:"number",value:i.securityDepositAmount||"",onChange:t=>s("securityDepositAmount",t.target.value?Number(t.target.value):void 0),placeholder:"Enter deposit amount",disabled:r})]})]})]})]}),e.jsxs("div",{children:[e.jsx(l,{htmlFor:"description",children:"Description *"}),e.jsx(W,{id:"description",value:i.description,onChange:t=>s("description",t.target.value),placeholder:"Describe the book's content, condition, and any other relevant details",rows:4,disabled:r,required:!0})]}),e.jsxs("div",{children:[e.jsx(l,{children:"Genres * (Select at least one)"}),e.jsx("div",{className:"grid grid-cols-3 md:grid-cols-4 gap-2 mt-2",children:de.map(t=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(w,{id:`genre-${t}`,checked:u.includes(t),onCheckedChange:()=>C(t),disabled:r}),e.jsx(l,{htmlFor:`genre-${t}`,className:"text-sm",children:t})]},t))})]}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[e.jsx(T,{type:"button",variant:"outline",onClick:n,disabled:r,children:"Cancel"}),e.jsx(T,{type:"submit",disabled:r,className:"flex items-center",children:r?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Update Book"]})})]})]})]})})}):null};export{fe as EditBookModal};
