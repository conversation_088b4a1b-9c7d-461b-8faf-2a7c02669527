import{p as f,r as i,j as e,i as r,U as y,N as b,J as o}from"./index-pJ8lwbxh.js";import{D as j,a as k,b as D,c as A,d as v,e as w}from"./dialog-DwQW96T5.js";import{A as N}from"./AdminLayout-B8-5mL2q.js";import{T as C}from"./trash-2-DP5EX241.js";import"./users-Buba-DeM.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=f("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),I=()=>{const[l,n]=i.useState(!1),[d,a]=i.useState(!1),[t,m]=i.useState(null),c=s=>{m(s),a(!0)},u=async()=>{try{n(!0),await b(),o.success("Sample books added to the database successfully!"),a(!1)}catch(s){console.error("Error seeding books:",s),o.error("Failed to seed books. Please try again.")}finally{n(!1)}},x=async()=>{try{o.success("Data purged successfully!"),a(!1)}catch(s){console.error("Error purging data:",s),o.error("Failed to purge data. Please try again.")}},h=()=>{t==="seed"?u():t==="purge"&&x()},g=[{title:"Seed Sample Books",description:"Add sample books to the database for testing",icon:e.jsx(S,{className:"h-8 w-8 text-burgundy-500"}),action:()=>c("seed"),color:"bg-burgundy-50"},{title:"Purge Test Data",description:"Remove test data from the database",icon:e.jsx(C,{className:"h-8 w-8 text-red-500"}),action:()=>c("purge"),color:"bg-red-50"}];return e.jsxs(N,{title:"Admin Tools",description:"Access administrative utilities and functions",children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Tools"}),e.jsx("p",{className:"text-gray-600",children:"Access admin tools and utilities"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map((s,p)=>e.jsx("div",{className:`rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${s.color}`,children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[s.icon,e.jsx("h2",{className:"text-xl font-semibold mt-4 mb-2",children:s.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:s.description}),e.jsx(r,{variant:"outline",className:"w-full",onClick:s.action,children:"Run Utility"})]})},p))}),e.jsx(j,{open:d,onOpenChange:a,children:e.jsxs(k,{children:[e.jsxs(D,{children:[e.jsx(A,{children:t==="seed"?"Seed Sample Books":"Purge Test Data"}),e.jsx(v,{children:t==="seed"?"Are you sure you want to add sample books to the database? This will create duplicate books if they already exist.":"Are you sure you want to purge test data from the database? This action cannot be undone."})]}),e.jsxs(w,{children:[e.jsx(r,{variant:"outline",onClick:()=>a(!1),children:"Cancel"}),e.jsx(r,{variant:t==="purge"?"destructive":"default",onClick:h,disabled:l,children:l?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm"})]})]})})]})};export{I as default};
