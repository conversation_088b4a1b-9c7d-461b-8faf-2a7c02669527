import{r,Q as N,J as c,j as e,H as S,O as v,U as w,i as d,k as E,L as p,o as A}from"./index-pJ8lwbxh.js";import{S as R}from"./shield-D84-_xNb.js";const C=()=>{const[m,x]=r.useState(!1),[u,y]=r.useState(!1),[h,n]=r.useState(null),[b,g]=r.useState(""),[j,l]=r.useState([]);r.useEffect(()=>{f()},[]),r.useEffect(()=>{const s=console.log,a=console.error;return console.log=(...t)=>{s(...t),l(o=>[...o,`LOG: ${t.map(i=>String(i)).join(" ")}`])},console.error=(...t)=>{a(...t),l(o=>[...o,`ERROR: ${t.map(i=>String(i)).join(" ")}`])},()=>{console.log=s,console.error=a}},[]);const f=async()=>{if(!m)try{x(!0),n(null),g(""),l([]);const s=await N();s.success?(y(!0),g(s.message),c.success("Admin user set up successfully!")):(n(s.message),c.error("Failed to set up admin"))}catch(s){console.error("Error setting up admin:",s),n(`Failed to set up admin: ${s instanceof Error?s.message:"Unknown error"}`),c.error("Failed to set up admin")}finally{x(!1)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(S,{}),e.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:e.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(R,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Setup"}),e.jsx("p",{className:"text-gray-600",children:"<NAME_EMAIL> as an admin user."})]}),h&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{className:"font-semibold",children:"Error:"}),e.jsx("p",{children:h})]}),u&&e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center justify-center",children:[e.jsx(v,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:b||"Admin user set up successfully!"})]}),m?e.jsxs("div",{className:"flex justify-center items-center py-4 mb-6",children:[e.jsx(w,{size:"md"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Setting up admin user..."})]}):e.jsx("div",{className:"flex justify-center mb-6",children:e.jsxs(d,{onClick:f,className:"flex items-center",children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),u?"Try Again":"Set Up Admin"]})}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Process Logs:"}),e.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:j.length>0?j.map((s,a)=>e.jsx("div",{className:`py-1 ${s.startsWith("ERROR")?"text-red-600":"text-gray-700"}`,children:s},a)):e.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet..."})})]}),e.jsxs("div",{className:"flex justify-center mt-8",children:[e.jsx(p,{to:"/",children:e.jsx(d,{variant:"outline",className:"mr-4",children:"Return to Home"})}),e.jsx(p,{to:"/admin",children:e.jsx(d,{children:"Go to Admin Dashboard"})})]})]})}),e.jsx(A,{})]})};export{C as default};
