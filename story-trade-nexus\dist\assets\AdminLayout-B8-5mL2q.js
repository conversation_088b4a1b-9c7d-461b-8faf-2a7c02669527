import{p as o,u as p,a1 as u,r as g,j as s,H as b,i as j,X as k,aL as N,a0 as t,L as y,a8 as f,o as w,ab as v,aM as M,a7 as A}from"./index-pJ8lwbxh.js";import{U as C}from"./users-Buba-DeM.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=o("BookCheck",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=o("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),E=({children:l,title:H="Admin Dashboard",description:O="Manage your book-sharing platform from here."})=>{var i;const{currentUser:a,signOut:r}=p(),c=u(),[n,d]=g.useState(!1),m=[{title:"Dashboard",icon:s.jsx(v,{className:"h-5 w-5"}),link:"/admin",description:"Admin dashboard overview"},{title:"Book Approvals",icon:s.jsx(S,{className:"h-5 w-5"}),link:"/admin/books",description:"Review and approve new book submissions"},{title:"User Management",icon:s.jsx(C,{className:"h-5 w-5"}),link:"/admin/users",description:"Manage users and permissions"},{title:"Contact Messages",icon:s.jsx(M,{className:"h-5 w-5"}),link:"/admin/messages",description:"View and manage contact messages from users"},{title:"Admin Tools",icon:s.jsx(A,{className:"h-5 w-5"}),link:"/admin/utilities",description:"Administrative utilities and functions"},{title:"Admin Settings",icon:s.jsx(L,{className:"h-5 w-5"}),link:"/admin/settings",description:"Configure admin preferences and system settings"}],h=async()=>{try{await r()}catch(e){console.error("Error signing out:",e)}},x=e=>c.pathname===e;return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(b,{}),s.jsxs("main",{className:"flex-grow flex flex-col md:flex-row",children:[s.jsx("div",{className:"md:hidden p-4 bg-white border-b",children:s.jsxs(j,{variant:"outline",size:"icon",onClick:()=>d(!n),className:"ml-auto flex",children:[n?s.jsx(k,{className:"h-5 w-5"}):s.jsx(N,{className:"h-5 w-5"}),s.jsx("span",{className:"sr-only",children:"Toggle menu"})]})}),s.jsxs("aside",{className:t("w-full md:w-64 bg-white shadow-md md:shadow-none transition-all duration-300 ease-in-out","md:block",n?"block":"hidden"),children:[s.jsxs("div",{className:"p-6 border-b",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-2",children:"Admin Panel"}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Welcome, ",(a==null?void 0:a.displayName)||((i=a==null?void 0:a.email)==null?void 0:i.split("@")[0])||"Admin"]})]}),s.jsxs("nav",{className:"p-4 space-y-1",children:[m.map(e=>s.jsxs(y,{to:e.link,className:t("flex items-center px-4 py-3 rounded-md transition-colors",x(e.link)?"bg-burgundy-50 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),title:e.description,children:[s.jsx("span",{className:"mr-3",children:e.icon}),s.jsx("span",{children:e.title})]},e.title)),s.jsxs("button",{onClick:h,className:"w-full flex items-center px-4 py-3 rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[s.jsx(f,{className:"h-5 w-5 mr-3"}),s.jsx("span",{children:"Sign Out"})]})]})]}),s.jsx("div",{className:"flex-1 p-4 md:p-8 bg-gray-50",children:s.jsx("div",{className:"max-w-5xl mx-auto",children:l})})]}),s.jsx(w,{})]})};export{E as A};
