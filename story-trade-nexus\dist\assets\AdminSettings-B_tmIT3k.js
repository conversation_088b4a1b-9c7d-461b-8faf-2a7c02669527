import{ah as _,r as o,ai as ce,at as U,j as e,aj as b,ak as y,aN as le,a0 as x,a$ as q,aJ as de,au as ue,b0 as me,b1 as fe,al as he,i as pe,U as xe,aO as p,I as N,J as M}from"./index-BHNYm6xc.js";import{A as ge}from"./AdminLayout-CDx-1-TV.js";import{u as be}from"./index-D263A0kh.js";import"./users-DtcHk0CF.js";var P="Switch",[ve,Fe]=_(P),[je,Ne]=ve(P),O=o.forwardRef((a,i)=>{const{__scopeSwitch:s,name:r,checked:c,defaultChecked:n,required:t,disabled:l,value:m="on",onCheckedChange:f,form:u,...d}=a,[h,ie]=o.useState(null),ne=ce(i,j=>ie(j)),I=o.useRef(!1),B=h?u||!!h.closest("form"):!0,[v=!1,oe]=U({prop:c,defaultProp:n,onChange:f});return e.jsxs(je,{scope:s,checked:v,disabled:l,children:[e.jsx(b.button,{type:"button",role:"switch","aria-checked":v,"aria-required":t,"data-state":D(v),"data-disabled":l?"":void 0,disabled:l,value:m,...d,ref:ne,onClick:y(a.onClick,j=>{oe(re=>!re),B&&(I.current=j.isPropagationStopped(),I.current||j.stopPropagation())})}),B&&e.jsx(ye,{control:h,bubbles:!I.current,name:r,value:m,checked:v,required:t,disabled:l,form:u,style:{transform:"translateX(-100%)"}})]})});O.displayName=P;var V="SwitchThumb",$=o.forwardRef((a,i)=>{const{__scopeSwitch:s,...r}=a,c=Ne(V,s);return e.jsx(b.span,{"data-state":D(c.checked),"data-disabled":c.disabled?"":void 0,...r,ref:i})});$.displayName=V;var ye=a=>{const{control:i,checked:s,bubbles:r=!0,...c}=a,n=o.useRef(null),t=be(s),l=le(i);return o.useEffect(()=>{const m=n.current,f=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(f,"checked").set;if(t!==s&&d){const h=new Event("click",{bubbles:r});d.call(m,s),m.dispatchEvent(h)}},[t,s,r]),e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...c,tabIndex:-1,ref:n,style:{...a.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function D(a){return a?"checked":"unchecked"}var G=O,ke=$;const g=o.forwardRef(({className:a,...i},s)=>e.jsx(G,{className:x("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...i,ref:s,children:e.jsx(ke,{className:x("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));g.displayName=G.displayName;const k=o.forwardRef(({className:a,...i},s)=>e.jsx("div",{ref:s,className:x("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i}));k.displayName="Card";const w=o.forwardRef(({className:a,...i},s)=>e.jsx("div",{ref:s,className:x("flex flex-col space-y-1.5 p-6",a),...i}));w.displayName="CardHeader";const C=o.forwardRef(({className:a,...i},s)=>e.jsx("h3",{ref:s,className:x("text-2xl font-semibold leading-none tracking-tight",a),...i}));C.displayName="CardTitle";const S=o.forwardRef(({className:a,...i},s)=>e.jsx("p",{ref:s,className:x("text-sm text-muted-foreground",a),...i}));S.displayName="CardDescription";const R=o.forwardRef(({className:a,...i},s)=>e.jsx("div",{ref:s,className:x("p-6 pt-0",a),...i}));R.displayName="CardContent";const we=o.forwardRef(({className:a,...i},s)=>e.jsx("div",{ref:s,className:x("flex items-center p-6 pt-0",a),...i}));we.displayName="CardFooter";var A="Tabs",[Ce,Be]=_(A,[q]),L=q(),[Se,F]=Ce(A),H=o.forwardRef((a,i)=>{const{__scopeTabs:s,value:r,onValueChange:c,defaultValue:n,orientation:t="horizontal",dir:l,activationMode:m="automatic",...f}=a,u=de(l),[d,h]=U({prop:r,onChange:c,defaultProp:n});return e.jsx(Se,{scope:s,baseId:ue(),value:d,onValueChange:h,orientation:t,dir:u,activationMode:m,children:e.jsx(b.div,{dir:u,"data-orientation":t,...f,ref:i})})});H.displayName=A;var z="TabsList",J=o.forwardRef((a,i)=>{const{__scopeTabs:s,loop:r=!0,...c}=a,n=F(z,s),t=L(s);return e.jsx(me,{asChild:!0,...t,orientation:n.orientation,dir:n.dir,loop:r,children:e.jsx(b.div,{role:"tablist","aria-orientation":n.orientation,...c,ref:i})})});J.displayName=z;var K="TabsTrigger",W=o.forwardRef((a,i)=>{const{__scopeTabs:s,value:r,disabled:c=!1,...n}=a,t=F(K,s),l=L(s),m=Y(t.baseId,r),f=Z(t.baseId,r),u=r===t.value;return e.jsx(fe,{asChild:!0,...l,focusable:!c,active:u,children:e.jsx(b.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":f,"data-state":u?"active":"inactive","data-disabled":c?"":void 0,disabled:c,id:m,...n,ref:i,onMouseDown:y(a.onMouseDown,d=>{!c&&d.button===0&&d.ctrlKey===!1?t.onValueChange(r):d.preventDefault()}),onKeyDown:y(a.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&t.onValueChange(r)}),onFocus:y(a.onFocus,()=>{const d=t.activationMode!=="manual";!u&&!c&&d&&t.onValueChange(r)})})})});W.displayName=K;var X="TabsContent",Q=o.forwardRef((a,i)=>{const{__scopeTabs:s,value:r,forceMount:c,children:n,...t}=a,l=F(X,s),m=Y(l.baseId,r),f=Z(l.baseId,r),u=r===l.value,d=o.useRef(u);return o.useEffect(()=>{const h=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(h)},[]),e.jsx(he,{present:c||u,children:({present:h})=>e.jsx(b.div,{"data-state":u?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":m,hidden:!h,id:f,tabIndex:0,...t,ref:i,style:{...a.style,animationDuration:d.current?"0s":void 0},children:h&&n})})});Q.displayName=X;function Y(a,i){return`${a}-trigger-${i}`}function Z(a,i){return`${a}-content-${i}`}var Re=H,ee=J,se=W,te=Q;const Ee=Re,ae=o.forwardRef(({className:a,...i},s)=>e.jsx(ee,{ref:s,className:x("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i}));ae.displayName=ee.displayName;const E=o.forwardRef(({className:a,...i},s)=>e.jsx(se,{ref:s,className:x("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i}));E.displayName=se.displayName;const T=o.forwardRef(({className:a,...i},s)=>e.jsx(te,{ref:s,className:x("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i}));T.displayName=te.displayName;const Me=()=>{const[a,i]=o.useState(!1);o.useState("general");const[s,r]=o.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),c=async()=>{try{i(!0),await new Promise(t=>setTimeout(t,1e3)),M.success("Settings saved successfully!")}catch(t){console.error("Error saving settings:",t),M.error("Failed to save settings. Please try again.")}finally{i(!1)}},n=(t,l,m)=>{r(f=>({...f,[t]:{...f[t],[l]:m}}))};return e.jsxs(ge,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),e.jsx(pe,{onClick:c,disabled:a,className:"mt-4 md:mt-0",children:a?e.jsxs(e.Fragment,{children:[e.jsx(xe,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),e.jsxs(Ee,{defaultValue:"general",className:"w-full",children:[e.jsxs(ae,{className:"mb-6",children:[e.jsx(E,{value:"general",children:"General"}),e.jsx(E,{value:"books",children:"Books"}),e.jsx(E,{value:"notifications",children:"Notifications"})]}),e.jsx(T,{value:"general",children:e.jsxs(k,{children:[e.jsxs(w,{children:[e.jsx(C,{children:"General Settings"}),e.jsx(S,{children:"Configure general platform settings"})]}),e.jsxs(R,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"siteName",children:"Site Name"}),e.jsx(N,{id:"siteName",value:s.general.siteName,onChange:t=>n("general","siteName",t.target.value)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"contactEmail",children:"Contact Email"}),e.jsx(N,{id:"contactEmail",type:"email",value:s.general.contactEmail,onChange:t=>n("general","contactEmail",t.target.value)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"enableRegistration",children:"Enable User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),e.jsx(g,{id:"enableRegistration",checked:s.general.enableRegistration,onCheckedChange:t=>n("general","enableRegistration",t)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),e.jsx(g,{id:"requireEmailVerification",checked:s.general.requireEmailVerification,onCheckedChange:t=>n("general","requireEmailVerification",t)})]})]})]})}),e.jsx(T,{value:"books",children:e.jsxs(k,{children:[e.jsxs(w,{children:[e.jsx(C,{children:"Book Settings"}),e.jsx(S,{children:"Configure book-related settings"})]}),e.jsxs(R,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"requireApproval",children:"Require Book Approval"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),e.jsx(g,{id:"requireApproval",checked:s.books.requireApproval,onCheckedChange:t=>n("books","requireApproval",t)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),e.jsx(N,{id:"maxBooksPerUser",type:"number",value:s.books.maxBooksPerUser.toString(),onChange:t=>n("books","maxBooksPerUser",parseInt(t.target.value))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),e.jsx(g,{id:"allowMultipleImages",checked:s.books.allowMultipleImages,onCheckedChange:t=>n("books","allowMultipleImages",t)})]})]})]})}),e.jsx(T,{value:"notifications",children:e.jsxs(k,{children:[e.jsxs(w,{children:[e.jsx(C,{children:"Notification Settings"}),e.jsx(S,{children:"Configure email and system notifications"})]}),e.jsxs(R,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),e.jsx(g,{id:"enableEmailNotifications",checked:s.notifications.enableEmailNotifications,onCheckedChange:t=>n("notifications","enableEmailNotifications",t)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),e.jsx(g,{id:"notifyOnNewUser",checked:s.notifications.notifyOnNewUser,onCheckedChange:t=>n("notifications","notifyOnNewUser",t)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(p,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),e.jsx(g,{id:"notifyOnBookSubmission",checked:s.notifications.notifyOnBookSubmission,onCheckedChange:t=>n("notifications","notifyOnBookSubmission",t)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(p,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),e.jsx(N,{id:"adminEmailRecipients",value:s.notifications.adminEmailRecipients,onChange:t=>n("notifications","adminEmailRecipients",t.target.value)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{Me as default};
