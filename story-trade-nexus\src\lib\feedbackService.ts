/**
 * Feedback Service for PeerBooks
 * 
 * Handles feedback submissions, email notifications, and rate limiting
 */

import { initializeFirebase, db } from './firebase';

export interface FeedbackSubmission {
  id?: string;
  name: string;
  email: string;
  subject: string;
  category: 'Bug Report' | 'Feature Request' | 'General Feedback' | 'Technical Support' | 'Account Issues';
  message: string;
  rating?: number; // 1-5 stars
  userAgent?: string;
  ipAddress?: string; // For rate limiting (server-side only)
  createdAt: any; // Firebase Timestamp
  isRead: boolean;
  readAt?: any; // Firebase Timestamp
  adminResponse?: string;
  respondedAt?: any; // Firebase Timestamp
}

export interface FeedbackStats {
  totalSubmissions: number;
  averageRating: number;
  categoryBreakdown: Record<string, number>;
  unreadCount: number;
}

/**
 * Rate limiting storage for client-side prevention
 */
const RATE_LIMIT_KEY = 'peerbooks_feedback_submissions';
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds
const MAX_SUBMISSIONS_PER_HOUR = 3;

/**
 * Check if user has exceeded rate limit
 */
export const checkRateLimit = (): { allowed: boolean; remainingTime?: number } => {
  try {
    const stored = localStorage.getItem(RATE_LIMIT_KEY);
    if (!stored) {
      return { allowed: true };
    }

    const submissions: number[] = JSON.parse(stored);
    const now = Date.now();
    
    // Filter out submissions older than the rate limit window
    const recentSubmissions = submissions.filter(timestamp => 
      now - timestamp < RATE_LIMIT_WINDOW
    );

    if (recentSubmissions.length >= MAX_SUBMISSIONS_PER_HOUR) {
      const oldestSubmission = Math.min(...recentSubmissions);
      const remainingTime = RATE_LIMIT_WINDOW - (now - oldestSubmission);
      return { allowed: false, remainingTime };
    }

    return { allowed: true };
  } catch (error) {
    console.warn('Error checking rate limit:', error);
    return { allowed: true };
  }
};

/**
 * Record a new submission for rate limiting
 */
const recordSubmission = (): void => {
  try {
    const stored = localStorage.getItem(RATE_LIMIT_KEY);
    const submissions: number[] = stored ? JSON.parse(stored) : [];
    const now = Date.now();
    
    // Add current submission
    submissions.push(now);
    
    // Clean up old submissions
    const recentSubmissions = submissions.filter(timestamp => 
      now - timestamp < RATE_LIMIT_WINDOW
    );
    
    localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify(recentSubmissions));
  } catch (error) {
    console.warn('Error recording submission:', error);
  }
};

/**
 * Sanitize user input to prevent XSS and other attacks
 */
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .substring(0, 5000); // Limit length
};

/**
 * Submit feedback to Firestore and trigger email notification
 */
export const submitFeedback = async (
  feedbackData: Omit<FeedbackSubmission, 'id' | 'createdAt' | 'isRead' | 'readAt' | 'userAgent'>
): Promise<string> => {
  try {
    // Check rate limit
    const rateLimitCheck = checkRateLimit();
    if (!rateLimitCheck.allowed) {
      const remainingMinutes = Math.ceil((rateLimitCheck.remainingTime || 0) / (60 * 1000));
      throw new Error(`Rate limit exceeded. Please wait ${remainingMinutes} minutes before submitting again.`);
    }

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Submitting feedback:', { ...feedbackData, message: '[REDACTED]' });

    // Sanitize all text inputs
    const sanitizedData = {
      ...feedbackData,
      name: sanitizeInput(feedbackData.name),
      email: sanitizeInput(feedbackData.email),
      subject: sanitizeInput(feedbackData.subject),
      message: sanitizeInput(feedbackData.message),
    };

    // Validate required fields
    if (!sanitizedData.name || !sanitizedData.email || !sanitizedData.subject || !sanitizedData.message) {
      throw new Error('All required fields must be filled');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedData.email)) {
      throw new Error('Please enter a valid email address');
    }

    // Create a reference to the feedback collection
    const feedbackRef = collection(db, 'feedback');

    // Add the feedback to Firestore
    const newFeedback = {
      ...sanitizedData,
      userAgent: navigator.userAgent,
      createdAt: serverTimestamp(),
      isRead: false
    };

    const docRef = await addDoc(feedbackRef, newFeedback);
    console.log('Feedback submitted successfully with ID:', docRef.id);

    // Record submission for rate limiting
    recordSubmission();

    // Note: Email notification would be handled by a server-side function
    // For now, we'll just log it
    console.log('Email notification would be sent to admin for feedback:', docRef.id);

    return docRef.id;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    throw error;
  }
};

/**
 * Get all feedback submissions (admin only)
 */
export const getAllFeedback = async (): Promise<FeedbackSubmission[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, orderBy, getDocs } = await import('firebase/firestore');

    // Create a query to get all feedback, ordered by creation date (newest first)
    const feedbackRef = collection(db, 'feedback');
    const feedbackQuery = query(feedbackRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(feedbackQuery);

    // Map the query results to an array of feedback submissions
    const feedback: FeedbackSubmission[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      feedback.push({
        id: doc.id,
        name: data.name || '',
        email: data.email || '',
        subject: data.subject || '',
        category: data.category || 'General Feedback',
        message: data.message || '',
        rating: data.rating,
        userAgent: data.userAgent,
        createdAt: data.createdAt,
        isRead: data.isRead || false,
        readAt: data.readAt || null,
        adminResponse: data.adminResponse,
        respondedAt: data.respondedAt
      });
    });

    console.log(`Fetched ${feedback.length} feedback submissions`);
    return feedback;
  } catch (error) {
    console.error('Error fetching feedback:', error);
    throw error;
  }
};

/**
 * Mark feedback as read (admin only)
 */
export const markFeedbackAsRead = async (feedbackId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    const feedbackRef = doc(db, 'feedback', feedbackId);
    await updateDoc(feedbackRef, {
      isRead: true,
      readAt: serverTimestamp()
    });

    console.log('Feedback marked as read:', feedbackId);
  } catch (error) {
    console.error('Error marking feedback as read:', error);
    throw error;
  }
};

/**
 * Get feedback statistics (admin only)
 */
export const getFeedbackStats = async (): Promise<FeedbackStats> => {
  try {
    const allFeedback = await getAllFeedback();
    
    const stats: FeedbackStats = {
      totalSubmissions: allFeedback.length,
      averageRating: 0,
      categoryBreakdown: {},
      unreadCount: 0
    };

    // Calculate statistics
    let totalRating = 0;
    let ratingCount = 0;

    allFeedback.forEach(feedback => {
      // Count by category
      stats.categoryBreakdown[feedback.category] = 
        (stats.categoryBreakdown[feedback.category] || 0) + 1;

      // Count unread
      if (!feedback.isRead) {
        stats.unreadCount++;
      }

      // Calculate average rating
      if (feedback.rating) {
        totalRating += feedback.rating;
        ratingCount++;
      }
    });

    // Calculate average rating
    if (ratingCount > 0) {
      stats.averageRating = Math.round((totalRating / ratingCount) * 10) / 10;
    }

    return stats;
  } catch (error) {
    console.error('Error getting feedback stats:', error);
    throw error;
  }
};

/**
 * Format remaining time for rate limit message
 */
export const formatRemainingTime = (milliseconds: number): string => {
  const minutes = Math.ceil(milliseconds / (60 * 1000));
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  const hours = Math.ceil(minutes / 60);
  return `${hours} hour${hours !== 1 ? 's' : ''}`;
};
